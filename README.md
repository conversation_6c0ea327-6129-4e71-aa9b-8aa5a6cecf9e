# 监控平台前端项目

这是一个基于 React + TypeScript + Vite 构建的现代化监控平台前端项目。
基于夜莺监控平台前端代码二次开发，原始代码：https://github.com/n9e/fe-v5/ 版本：5.14.1。

## 新增功能特性

### 1. 智能诊断模块

智能诊断模块是一个基于 Dify.AI 的智能诊断系统，提供以下核心功能：

#### 1.1 诊断模块管理
- **模块配置**：支持创建、编辑、删除诊断模块
- **灵活配置**：可配置不同的诊断场景和规则
- **批量管理**：支持多个诊断模块的并行管理

#### 1.2 诊断流程
- **实时诊断**：支持实时运行诊断任务
- **流式输出**：采用 SSE（Server-Sent Events）实现诊断结果的流式展示
- **结果展示**：支持 Markdown 格式的诊断报告，包含表格、代码块等富文本内容

#### 1.3 Dify.AI 集成
- **工作流集成**：支持 Dify Workflow API 的无缝对接
- **配置项**：
  ```json
  {
    "apiKey": "your-api-key",
    "endpoint": "https://api.dify.ai/v1",
    "workflowId": "your-workflow-id"
  }
  ```
- **安全性**：API 密钥等敏感信息进行加密存储

### 2. 其他功能模块(保留原始功能)

## 快速开始

### 环境要求
```bash
node: v16.13.0
npm: 8.1.0
```

### 安装
```bash
npm install
```

### 开发
```bash
npm run dev
```

### 构建
```bash
npm run build
```

## 部署配置

### Nginx 配置
```nginx
server {
    listen       8765;
    server_name  _;

    add_header Access-Control-Allow-Origin *;
    add_header 'Access-Control-Allow-Credentials' 'true';
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
    root   front-end/page/path;    # e.g. /root/n9e/pub;

    location / {
        root front-end/page/path;    # e.g. /root/n9e/pub;
        try_files $uri /index.html;
    }
    location /api/ {
        proxy_pass http://n9e.api.server;   # e.g. 127.0.0.1:18000 
    }
}
```

### 开发注意事项
1. `vite.config.js` 和 `tsconfig.json` 需要同时配置以确保别名正常工作
2. 在 VSCode 中添加 `"css.validate": false` 到 setting.json 以忽略 CSS 警告
3. 智能诊断模块的 Dify.AI 配置需要在部署前完成设置

## 智能诊断开发指南

### 1. 创建诊断模块
1. 访问 `/monitor/ai-diagnosis` 路径
2. 点击"添加诊断模块"按钮
3. 填写以下信息：
   - 模块名称（必填）
   - 描述信息（选填）
   - Dify API 配置（必填，JSON 格式）

### 2. 运行诊断
1. 在诊断模块列表中找到目标模块
2. 点击"运行诊断"按钮
3. 等待诊断结果，支持实时展示诊断进度

### 3. 查看诊断结果
- 诊断结果支持 Markdown 格式
- 包含以下内容：
  - 诊断概要
  - 详细分析
  - 解决方案
  - 参考建议

### 4. 自定义开发
如需扩展诊断功能，可以：
1. 在 `src/services/aiDiagnosis.ts` 中添加新的诊断方法
2. 在 `src/pages/aiDiagnosis/components/` 中添加新的展示组件
3. 在 Dify.AI 平台配置对应的诊断工作流

## 贡献指南
欢迎提交 Issue 和 Pull Request 来帮助改进项目。

## 许可证
Apache License 2.0
