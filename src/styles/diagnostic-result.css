.diagnostic-result {
  padding: 1.5rem;
  background-color: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.diagnostic-result h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 2rem;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 0.5rem;
}

.diagnostic-result h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.diagnostic-result h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.diagnostic-result p {
  margin-bottom: 1rem;
  line-height: 1.7;
  color: #4a5568;
}

.diagnostic-result ul,
.diagnostic-result ol {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.diagnostic-result li {
  margin-bottom: 0.75rem;
  line-height: 1.6;
}

.diagnostic-result code {
  background-color: #f7fafc;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-family: 'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, monospace;
  font-size: 0.875rem;
  color: #2d3748;
}

.diagnostic-result pre {
  background-color: #f7fafc;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin-bottom: 1.5rem;
  border: 1px solid #e2e8f0;
}

.diagnostic-result blockquote {
  border-left: 4px solid #4299e1;
  padding-left: 1rem;
  margin-bottom: 1.5rem;
  color: #4a5568;
  background-color: #ebf8ff;
  padding: 1rem;
  border-radius: 0.5rem;
}

.diagnostic-result hr {
  border: 0;
  border-top: 2px solid #e2e8f0;
  margin: 2.5rem 0;
}

.diagnostic-result table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 2rem;
  background-color: #ffffff;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.diagnostic-result th,
.diagnostic-result td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.diagnostic-result th {
  background-color: #f7fafc;
  font-weight: 600;
  color: #2d3748;
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 0.05em;
}

.diagnostic-result td {
  color: #4a5568;
  line-height: 1.5;
}

.diagnostic-result tr:last-child td {
  border-bottom: none;
}

.diagnostic-result tr:nth-child(even) {
  background-color: #f7fafc;
}

.diagnostic-result tr:hover {
  background-color: #edf2f7;
}

.diagnostic-result details {
  margin: 1.5rem 0;
  border-radius: 0.5rem;
  background-color: #f8f8f8;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.diagnostic-result details summary {
  padding: 1rem;
  background-color: #f0f0f0;
  cursor: pointer;
  font-weight: 600;
  color: #2d3748;
  user-select: none;
  transition: background-color 0.2s;
}

.diagnostic-result details summary:hover {
  background-color: #e8e8e8;
}

.diagnostic-result details[open] summary {
  border-bottom: 1px solid #e2e8f0;
}

.diagnostic-result details > div {
  padding: 1rem;
  color: #4a5568;
  line-height: 1.6;
  background-color: #ffffff;
}

.diagnostic-result strong {
  color: #2d3748;
  font-weight: 600;
}

.diagnostic-result a {
  color: #4299e1;
  text-decoration: none;
  transition: color 0.2s;
}

.diagnostic-result a:hover {
  color: #2b6cb0;
  text-decoration: underline;
}

.ant-modal-content {
  border-radius: 0.75rem;
  overflow: hidden;
}

.ant-modal-header {
  background-color: #f7fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem 1.5rem;
}

.ant-modal-body {
  padding: 1.5rem;
  max-height: 80vh;
  overflow-y: auto;
}

.ant-modal-title {
  color: #2d3748;
  font-weight: 600;
  font-size: 1.25rem;
}

.diagnostic-result ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.diagnostic-result ::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 4px;
}

.diagnostic-result ::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
}

.diagnostic-result ::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
} 