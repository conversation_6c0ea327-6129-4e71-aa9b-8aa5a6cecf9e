.ts-graph-tooltip {
  visibility: hidden;
  position: fixed;
  pointer-events: none;
  width: 100%;
}
.ts-graph-tooltip-content {
  font-size: 12px;
  font-family: "Lucida Grande", "Lucida Sans Unicode", Arial, Helvetica, sans-serif;
  font-size: 12px;
  white-space: nowrap;
  color: rgb(51, 51, 51);
  margin-left: 0px;
  margin-top: 0px;
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid silver;
  border-radius: 3px;
  box-shadow: 1px 1px 1px #888;
  white-space: nowrap;
  padding: 5px;
  position: fixed;
  z-index: 9999;
}
.ts-graph-tooltip ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.ts-graph-tooltip ul li {
  table-layout: fixed;
  word-wrap: break-word;
  white-space: normal;
}
.ts-graph-zoom {
  position: relative;
}
.ts-graph-legend {
  position: relative;
  padding: 0 10px;
}
.ts-graph-legend-item {
  cursor: pointer;
  display: inline-block;
  margin-right: 10px;
  word-break: break-all;
}
.ts-graph-legend-item-symbol {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  width: 20px;
  height: 20px;;
  margin: 0 5px;
}
.ts-graph-legend-item-symbol-line {
  position: absolute;
  display: inline-block;
  width: 20px;
  top: 9px;
  left: 0;
  border-width: 1px;
  border-style: solid;
}
.ts-graph-legend-item-symbol-point {
  position: absolute;
  display: inline-block;
  width: 10px;
  height: 10px;
  top: 5px;
  left: 5px;
  border-radius: 10px;
}
.ts-graph-zoom-marker {
  position: absolute;
  background: rgba(51,92,173,0.25);
}
.ts-graph-zoom-resetBtn {
  display: none;
  position: absolute;
  z-index: 1;
  top: 10px;
  right: 10px;
  background: #f7f7f7;
  font-size: 12px;
  text-align: center;
  width: 84px;
  height: 31px;
  line-height: 31px;
  border: 1px solid #ccc;
  cursor: pointer;
  color: #333;
}
.ts-graph-zoom-resetBtn:hover {
  background: #e6e6e6;
}
