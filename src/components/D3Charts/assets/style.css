/*
 * Copyright 2022 Nightingale Team
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
.ts-graph-tooltip {
  visibility: hidden;
  position: absolute;
  pointer-events: none;
  width: 100%;
}
.ts-graph-tooltip-content {
  font-family: "Lucida Grande", "Lucida Sans Unicode", Arial, Helvetica, sans-serif;
  font-size: 12px;
  position: absolute;
  color: #333333;
  margin-left: 0px;
  margin-top: 0px;
  left: 8px;
  top: 8px;
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid silver;
  border-radius: 3px;
  box-shadow: 1px 1px 1px #888;
  white-space: nowrap;
  padding: 5px;
}
.ts-graph-tooltip ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.ts-graph-tooltip ul li {
  table-layout: fixed;
  word-wrap: break-word;
  white-space: normal;
}
.ts-graph-zoom {
  position: relative;
}
.ts-graph-zoom-marker {
  position: absolute;
  background: rgba(51, 92, 173, 0.25);
}
.ts-graph-zoom-resetBtn {
  display: none;
  position: absolute;
  z-index: 1;
  top: 10px;
  right: 10px;
  background: #f7f7f7;
  font-size: 12px;
  text-align: center;
  width: 84px;
  height: 31px;
  line-height: 31px;
  border: 1px solid #ccc;
  cursor: pointer;
  color: #333;
}
.ts-graph-zoom-resetBtn:hover {
  background: #e6e6e6;
}
