@import '../../style/index.less';
@import 'antd/es/style/themes/default.less';

@import '../../query-form/style/index.less';

@dantd-data-table-prefix-cls: ~'@{dantd-prefix}-data-table';

.@{dantd-data-table-prefix-cls} {
  &-query {
    // padding: 22px 20px 0;
    // background: #fff;
    margin-bottom: 20px;

    &-compact {
      margin-bottom: 0;
      padding: 0;

      .dantd-query-form {
        padding: 0px 10px 0;
        margin-bottom: 3px;
      }
      .dantd-query-form-formitem {
        margin-bottom: 3px;
      }
      .dantd-query-form-option {
        .ant-form-item {
          margin-bottom: 3px;
        }
      }
    }
  }

  &-body {
    padding: 16px 20px;
    background: #fff;

    &-compact {
      margin-bottom: 0;
      padding: 0;
    }
  }

  &-header {
    &-title {
    }
    &-search {
      display: flex;
      justify-content: space-between !important;
      flex-wrap: nowrap !important;
      align-items: center !important;
      margin-bottom: 10px;
    }
    &-loadbtn {
      display: inline-block;
      cursor: pointer;
      transition: all 0.5s;
      &-icon {
      }
      &-btn {
        color: #bfbfbf !important;
        font-size: 12px !important;
        transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);

        &:hover {
          color: @primary-color !important;
        }
      }
      &-right {
        margin-left: 10px;
      }
      &-left {
        margin-right: 10px;
      }

      &:hover {
        color: @primary-color;
      }
    }
    &-custom {
      margin-bottom: 10px;
    }
    &-filter {
      display: flex !important;
      align-items: flex-end;
      margin-bottom: 10px;
      padding: 0;

      &-line {
        display: flex;
        flex: 0 1 auto;
        flex-wrap: wrap;
        text-align: left;
        align-items: center;

        &-item {
          margin-left: 5px;
          margin-top: 5px;

          > button.table-header-item-btn {
            background-color: #fafafa;
            display: flex;
            align-items: center;
            justify-content: center;
            max-width: 200px;
            overflow: hidden;

            > h1,
            h2,
            h3,
            h4,
            h5,
            h6,
            p,
            pre {
              margin-bottom: 0;
            }

            > span.table-header-item-btn-content {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }

      &-left {
        // display: flex !important;
        // flex: 0 1 auto !important;
        // flex-wrap: wrap;
        // text-align: left;
        // align-items: flex-end;
        // max-width: 50%;

        &-minh {
          min-height: 32px;
        }
      }

      &-right {
        // display: flex !important;
        // flex: 0 1 auto !important;
        // flex-wrap: wrap;
        // text-align: right;
        // justify-content: flex-end;
        // align-items: center;
        // max-width: 50%;

        &-item {
          margin-top: 5px;
          margin-left: 5px;

          > button.table-header-item-btn {
            background-color: #fafafa;
            display: flex;
            align-items: center;
            justify-content: center;
            max-width: 200px;
            overflow: hidden;

            > h1,
            h2,
            h3,
            h4,
            h5,
            h6,
            p,
            pre {
              margin-bottom: 0;
            }

            > span.table-header-item-btn-content {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }

  &-table {
    &-wrapper {
      background: #fff;
      max-width: 100%;
      overflow-x: auto;
    }

    &-content {
      position: relative;
      // min-width: fit-content;
      width: auto;

      &-noborder {
        padding: 0 !important;
        border: none !important;
      }

      &-select-num {
        position: absolute;
        bottom: 34px;
        left: 26px;
      }
      .ant-table {
        margin-top: 0;
      }
      // .ant-table-content {
      //   overflow: auto;
      // }
    }
  }
}
