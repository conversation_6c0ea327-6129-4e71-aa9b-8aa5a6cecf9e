/*
 * Copyright 2022 Nightingale Team
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
export default {
  back: 'Back',
  'table.search.placeholder':
    'Fuzzy search table content (multiple keywords separated by Spaces. Such as: key1 key2)',
  'table.sort.ascend': 'Ascend',
  'table.sort.descend': 'Descend',
  'table.total.prefix': 'Total',
  'table.total.suffix': 'Items',
  'table.select.num': 'Selected: ',
  'table.filter.search.placeholder': 'Please enter keywords',
  'table.filter.search.btn.ok': 'Search',
  'table.filter.search.btn.cancel': 'Clear',
  'table.filter.header.title': 'Filtration Conditions:',
  'table.filter.header.search': ': Search',
  'table.filter.header.filter': ': Filter',
  'table.filter.header.btn.clear': 'Clear',
  'form.item.key.title': 'Custom Key',
  'form.item.value.title': 'Custom Value',
  'form.detail.nodata': 'No Data',
  'form.item.key.placeholder': 'Please enter key',
  'form.item.value.placeholder': 'Please enter value',
  'form.item.add': 'Add Custom Parameters',
  'form.placeholder.prefix': 'Please enter',
  'form.selectplaceholder.prefix': 'Please select',
  'queryform.reset': 'Clear',
  'queryform.search': 'Search',
  'queryform.collapsed': 'Collapsed',
  'queryform.expand': 'Expand',
  'color.placeholder': 'Please select color',
};
