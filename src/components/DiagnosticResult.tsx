import React from 'react';
import { formatDiagnosticResult } from '../utils/formatDiagnosticResult';
import { DiagnosticResult } from '../types/diagnostic';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import '../styles/diagnostic-result.css';

interface DiagnosticResultProps {
  result: DiagnosticResult;
}

export const DiagnosticResultView: React.FC<DiagnosticResultProps> = ({ result }) => {
  const markdown = formatDiagnosticResult(result);

  return (
    <div className="diagnostic-result">
      <ReactMarkdown 
        remarkPlugins={[remarkGfm]}
        components={{
          // 自定义表格样式
          table: ({ children }) => (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                {children}
              </table>
            </div>
          ),
          th: ({ children }) => (
            <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {children}
            </td>
          ),
          // 自定义 details 标签样式
          details: ({ children }) => (
            <details className="diagnostic-details">
              {children}
            </details>
          ),
          summary: ({ children }) => (
            <summary className="diagnostic-summary">
              {children}
            </summary>
          ),
          // 自定义代码块样式
          code: ({ node, inline, className, children, ...props }) => {
            const match = /language-(\w+)/.exec(className || '');
            return !inline && match ? (
              <pre className="diagnostic-code-block">
                <code className={className} {...props}>
                  {children}
                </code>
              </pre>
            ) : (
              <code className="diagnostic-inline-code" {...props}>
                {children}
              </code>
            );
          },
          // 自定义引用块样式
          blockquote: ({ children }) => (
            <blockquote className="diagnostic-blockquote">
              {children}
            </blockquote>
          ),
          // 自定义分割线样式
          hr: () => <hr className="diagnostic-hr" />,
        }}
      >
        {markdown}
      </ReactMarkdown>
    </div>
  );
}; 