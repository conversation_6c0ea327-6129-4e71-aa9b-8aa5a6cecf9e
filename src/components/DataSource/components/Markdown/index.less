.srm-markdown-wrapper {
  h1,
  h2,
  h3,
  h4,
  h5 {
    // font-family: PingFangSC-Medium;
    // 修复markdown的字体与其他页面字体不一致的差异
    font-family: @font-family;
    color: @fontColorLevel1;
    margin: 0;
  }
  h1 {
    font-size: 16px;
    line-height: 24px;
  }
  h2 {
    font-size: @baseFontSizeSmall;
    line-height: 52px;
    height: 52px;
    display: flex;
    align-items: center;
    &::before {
      width: 4px;
      height: 14px;
      background-color: @primary-color;
      border-radius: 2px;
      margin-right: 8px;
      opacity: 1;
      content: ' ';
      display: inline-block;
    }
  }
  h3 {
    font-size: @baseFontSizeTiny;
  }
  p {
    margin-bottom: 0;
  }
  img {
    width: 90%;
  }
  pre {
    box-sizing: border-box;
    width: 100%;
    padding: 12px;
    background: #f2f2f2;
    border-radius: 2px;
    font-family: PingFangSC-Regular;
    margin-right: 0;
    code {
      color: #6c53b1;
      font-size: @baseFontSizeTiny;
      line-height: 20px;
    }
  }
  p {
    font-family: PingFangSC-Regular;
    font-size: @baseFontSizeTiny;
    color: @fontColorLevel2;
    line-height: 28px;
  }
  table {
    width: 100%;
    thead {
      width: 100%;
      height: 36px;
      background: @baseTableHeaderBgColor;
      th {
        font-family: PingFangSC-Medium;
        font-size: @baseFontSizeTiny;
        color: @fontColorLevel2;
        line-height: 36px;
        padding: 0 0 0 16px;
        text-align: left;
      }
    }
    tbody {
      tr {
        height: 36px;
        border-bottom: 1px solid @baseContentBgColor;
        td {
          font-family: PingFangSC-Regular;
          font-size: @baseFontSizeTiny;
          color: @fontColorLevel2;
          line-height: 36px;
          padding: 0 0 0 16px;
          text-align: left;
        }
      }
    }
  }
  p + h1,
  p + h2,
  p + h3 {
    margin-top: 24px;
  }
  table + h1,
  table + h2,
  table + h3 {
    margin-top: 24px;
  }
  pre + h1,
  pre + h2,
  pre + h3 {
    margin-top: 24px;
  }
  p + p,
  p + table,
  table + p {
    margin-top: 8px;
  }
  h1 + p,
  h2 + p,
  h3 + p,
  h4 + p {
    margin-top: 12px;
  }
  h1 + table,
  h2 + table,
  h3 + table,
  h4 + table {
    margin-top: 12px;
  }
  h1 + pre,
  h2 + pre,
  h3 + pre,
  h4 + pre {
    margin-top: 12px;
  }
  li {
    // list-style: none;
    line-height: 28px;
  }
  ul {
    padding-inline-start: 24px;
  }
}
