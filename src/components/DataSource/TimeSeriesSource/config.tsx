import React from 'react';

export const sourceMap = [
  {
    logo: <img src={'/monitor/logos/prometheus_logo.svg'} alt='' className='prometheus_logo' width='46' />,
    name: 'Prometheus',
  },
  {
    logo: <img src={'/monitor/logos/zabbix_logo.svg'} alt='' className='prometheus_logo' width='94' />,
    name: 'Zabbix',
  },
  {
    logo: <img src={'/monitor/logos/mysql_logo.svg'} alt='' className='prometheus_logo' width='89' />,
    name: 'MySQL',
  },
  {
    logo: <img src={'/monitor/logos/kafka_logo.svg'} alt='' className='prometheus_logo' width='37' />,
    name: 'Kafka',
  },
];

export const sourceLogoMap = {
  oracle: <img src={'/monitor/logos/oracle_logo.svg'} alt='' className='prometheus_logo' width='89' height='46' />,
  prometheus: <img src={'/monitor/logos/prometheus_logos.png'} alt='' className='prometheus_logo' width='80' />,
  zabbix: <img src={'/monitor/logos/zabbix_logo.svg'} alt='' className='prometheus_logo' width='94' />,
  mysql: <img src={'/monitor/logos/mysql_logo.svg'} alt='' className='prometheus_logo' width='89' />,
  kafka: <img src={'/monitor/logos/kafka_logo.svg'} alt='' className='prometheus_logo' width='37' />,
  elasticsearch: <img src={'/monitor/logos/elasticSearch.svg'} alt='' className='prometheus_logo' width='46' />, // 兼容 n9e
  'elasticsearch.logging': <img src={'/monitor/logos/elasticSearch.svg'} alt='' className='prometheus_logo' width='46' />,
  'tencent-es.logging': <img src={'/monitor/logos/tencet-es.png'} alt='' className='prometheus_logo' width='61' />,
  'aliyun-es.logging': <img src={'/monitor/logos/aliyun-es.png'} alt='' className='prometheus_logo' width='61' />,
  'aliyun-sls.logging': <img src={'/monitor/logos/aliyun-sls.png'} alt='' className='prometheus_logo' width='46' />,
  'kafka.logging': <img src={'/monitor/logos/kafka_logo--simple.png'} alt='' className='prometheus_logo' width='36' />,
  'zipkin.tracing': <img src={'/monitor/logos/zipkin_logo.png'} alt='' className='prometheus_logo' width='39' />,
  'jaeger.tracing': <img src={'/monitor/logos/jaeger_logo.png'} alt='' className='prometheus_logo' width='40' />,
  'skywalking.tracing': <img src={'/monitor/logos/skywalking_logo.png'} alt='' className='prometheus_logo' width='130' />,
  'standard.change': <img src={'/monitor/logos/custom_logo.svg'} alt='' className='prometheus_logo' width='41' />,
  'standard.alert': <img src={'/monitor/logos/custom_logo.svg'} alt='' className='prometheus_logo' width='41' />,
  'prometheus.alert': <img src={'/monitor/logos/prometheus_logo.svg'} alt='' className='prometheus_logo' width='46' />,
  'zabbix.alert': <img src={'/monitor/logos/zabbix_logo.svg'} alt='' className='prometheus_logo' width='94' />,
  'n9e.alert': <img src={'/monitor/logos/nightingale_logo.svg'} alt='' className='prometheus_logo' width='36' />,
  'open-falcon.alert': <img src={'/monitor/logos/open_falcon_logo.png'} alt='' className='prometheus_logo' width='45' />,
  'tencent-cm.alert': <img src={'/monitor/logos/腾讯云.png'} alt='' className='prometheus_logo' width='60' />,
  'jira.change': <img src={'/monitor/logos/Jira.svg'} alt='' className='prometheus_logo' width='120' />,
};
