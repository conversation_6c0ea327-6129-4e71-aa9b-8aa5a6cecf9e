.datasource-form-mysql-shard {
  background-color: #f6f6f6;
  padding: 16px;
  margin-top: 10px;
  position: relative;
  .ant-form-item-control-input {
    min-height: unset;
  }
}

.datasource-form-mysql-shard-success {
  border: 1px solid #52c41a;
}

.datasource-form-mysql-shard-error {
  border: 1px solid #ff3b30;
}

.datasource-form-mysql-shard-close {
  position: absolute;
  top: 5px;
  right: 5px;
}

.datasource-form-mysql-shards-preview {
  background-color: #f6f6f6;
  padding: 16px;
  a {
    margin-right: 10px;
  }
}

.datasource-form-mysql-conn-msg {
  padding-left: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  height: 120px;
  color: #8c8c8c;
  .f14 {
    font-size: 14px;
  }
  .anticon {
    margin-right: 8px;
  }
}

.datasource-form-mysql-tables {
  background-color: #f6f6f6;
  padding: 16px;

  .icon-align {
    height: 34px;
    line-height: 34px;
    font-size: 16px;
  }
}

.datasource-form-mysql-table-title-name {
  font-weight: 400;
  color: #262626;
}

.datasource-form-mysql-table-title-desc {
  font-weight: 400;
  color: #8c8c8c;
}

.datasource-form-mysql-tables-review,
.datasource-form-mysql-tips {
  border: 1px dashed #e5e5e5;
  padding: 16px;
  border-radius: 2px;
  margin-bottom: 10px;

  strong {
    font-size: 14px;
    font-weight: 500;
    color: #262626;
  }
  ul {
    margin: 0;
    padding: 0;
    color: #8c8c8c;
  }
  ul li {
    list-style-type: none;
    margin-bottom: 5px;
  }
}

.datasource-form-mysql-account,
.datasource-form-mysql-conn-result {
  height: 160px;
  border: 1px solid #e5e5e5;
  padding: 16px;
}

.datasource-form-mysql-account-noteditable-text {
  color: #bfbfbf;
}

.match-table-modal {
  .ant-modal-body {
    padding-top: 16px;
    height: 400px;
    overflow: auto;
  }
}
.datasource-form-icons {
  font-size: 14px;
  margin-left: 8px;
}
