.left-area {
  &.collapse {
    width: 0px;
    padding: 0px;
    margin: 0px;
    .collapse-btn {
      right: 0px;
    }
  }
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  // width: 200px;
  height: 100%;
  flex-shrink: 0;
  position: relative;
  padding: 16px;
  // margin-right: 10px;
  &-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    &-title {
      color: @fontColorLevel2;
      font-size: @baseFontSizeSmall;
      font-weight: bold;
      line-height: 36px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    &-search {
      margin-bottom: 10px;
    }
    .radio-list {
      flex-shrink: 1;
      overflow: auto;
      .radio-list-group-item {
        margin-right: 0;
      }
      &-group,
      &-group-space,
      &-group-item {
        width: 100%;
      }
      // &-item {
      //   padding: 6px 8px;
      //   &:hover {
      //     cursor: pointer;
      //     background-color: @colorGray2;
      //   }
      //   &.select-list-item-active {
      //     background-color: @colorLightBlue;
      //     &:hover {
      //       background-color: @colorLightBlue;
      //     }
      //   }
      // }
    }
    &:last-of-type {
      margin-bottom: 0;
    }
    &.group-shrink {
      flex: initial;
      overflow: auto;
    }
    .ant-checkbox-group-item {
      display: flex;
      margin-bottom: 6px;
    }
  }
  .collapse-btn {
    cursor: pointer;
    position: absolute;
    display: flex;
    align-items: center;
    top: 50%;
    margin-top: -29px;
    right: -10px;
    height: 58px;
    width: 10px;
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.1);
  }
}
