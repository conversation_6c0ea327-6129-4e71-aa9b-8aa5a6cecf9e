.fc-menu-icon {
  display: flex;
  width: 48px;
  // height: 48px;
  align-items: center;
  justify-content: center;
  z-index: 1;
  .iconpark-icon {
    width: 16px;
  }
}
.fc-menu-label {
  padding-left: 8px;
  font-size: 14px;
}

.fc-menu-bay {
  background-color: #f9f9f9;
  padding-top: 10px;
  padding-bottom: 10px;
  & > .fc-menu-sub-top {
    position: absolute;
    width: 100%;
    top: 0px;
    height: 15px;
    border-radius: 0 0 15px 0;
    background-color: @primary-color;
  }
  & > .fc-menu-sub-bottom {
    position: absolute;
    width: 100%;
    bottom: 0px;
    height: 15px;
    z-index: 1;
    border-radius: 0 15px 0 0;
    background-color: @primary-color;
  }
  & > .fc-menu-sub-left {
    position: absolute;
    height: 100%;
    width: 8px;
    top: 0px;
    left: 0px;
    background-color: @primary-color;
    z-index: 1;
  }
  & > .fc-menu-sub-topleft-corner {
    position: absolute;
    height: 16px;
    width: 16px;
    top: 15px;
    left: 8px;
    background-color: @primary-color;
    &::before {
      display: block;
      content: '';
      width: 100%;
      height: 100%;
      border-radius: 16px 0 0 0;
      background: #f9f9f9;
    }
  }
  .fc-menu-sub-bottomleft-corner {
    position: absolute;
    height: 16px;
    width: 16px;
    bottom: 15px;
    left: 8px;
    z-index: 1;
    background-color: @primary-color;
    &::before {
      display: block;
      content: '';
      width: 100%;
      height: 100%;
      border-radius: 0 0 0 16px;
      background: #f9f9f9;
    }
  }
}

.fc-menu {
  border-radius: 0 0 8px 8px;
  &.collapsed {
    width: 48px;
    .fc-menu-item-icon,
    .fc-menu-sub-title-icon {
      height: 48px;
    }
    .fc-menu-sub,
    .fc-menu-item {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }
  &.hide {
    width: 0px;
  }
  flex: 1;
  width: 152px;
  background-color: @primary-color;
  color: #fff;
  cursor: pointer;
  &-item {
    line-height: 48px;
    display: flex;
    position: relative;
    &.fc-menu-item-selected {
      line-height: 45px;
      color: @primary-color;
      .fc-menu-bay();
    }

    &:hover {
      color: #bba6fa;
    }
    &-icon {
      .fc-menu-icon();
    }
    &-label {
      .fc-menu-label();
    }
  }

  &-sub {
    position: relative;
    // transition: 0.3s all;
    // height: 48px;
    overflow: hidden;
    &-open {
      height: auto;
    }
    &-selected {
      height: auto;
      padding-top: 10px;
      padding-bottom: 10px;
      background-color: #f9f9f9;
      color: #666;
      .fc-menu-sub-title,
      .fc-menu-item-selected {
        color: @primary-color;
        padding: 0;
      }
      .fc-menu-bay();
    }
    &-title {
      font-size: 14px;
      line-height: 48px;
      display: flex;
      &-icon {
        .fc-menu-icon();
      }
      &-label {
        .fc-menu-label();
      }
    }
    .fc-menu-item {
      height: 45px;
      line-height: 34px;
      &-label {
        .fc-menu-label();
        font-size: 12px;
      }
    }
  }
  .collapse-btn {
    &:hover {
      background: fade(@primary-color, 80%);
    }
    width: 24px;
    height: 24px;
    position: fixed;
    left: 0px;
    top: 0px;
    color: #fff;
    background: fade(@primary-color, 30%);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
  }
}
.fc-menu-sub-tooltip,
.fc-skim-menu-tooltip {
  .ant-tooltip-inner {
    padding: 0px;
    border-radius: 7px;
  }
  .ant-tooltip-arrow {
    display: none;
  }
}
.fc-skim-menu-tooltip {
  left: 10px !important;
  top: 18px !important;
}
.fc-skim-menu {
  width: 218px;
  border-radius: 7px;
  padding: 16px 24px;
  background: @primary-color;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  &-primary {
    & > div {
      &:hover {
        font-size: 14px;
        background: #310094;
        border-radius: 7px;
      }
      padding: 0px 8px;
      height: 34px;
      line-height: 34px;
    }
  }
  &-secondary {
    margin-left: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    &-wrap {
      border-left: 2px solid #310094;
      & > div {
        position: relative;
        color: lightgray;
        &:hover {
          color: @primary-background;
          &::before {
            content: '';
            position: absolute;
            top: 0px;
            left: -2px;
            height: 30px;
            width: 2px;
            background: #9470ff;
          }
        }
        padding: 6px;
      }
    }
  }
}
