.chart-wrapper {
  // margin-top: 16px;
  border: 1px solid #e0dee2;
  height: 100%;
  flex-direction: column;
  display: flex;
  background: #fff;
  .chart-title {
    background-color: #fafafa;
    padding-left: 15px;
    padding-right: 15px;
    font-weight: bold;
    border-bottom: 1px solid #e0dee2;
    padding-top: 10px;
    padding-bottom: 10px;
    display: flex;
    justify-content: space-between;
    &-label {
      display: flex;
      align-items: center;
    }
    &-right-bar {
      flex-shrink: 0;
    }
    div {
      span {
        cursor: pointer;
        &:hover {
          color: @primary-color;
        }
      }
    }
  }
  .chart-filter {
    padding-left: 15px;
    margin-top: 10px;
    display: inline-flex;
    flex-wrap: wrap;
    align-items: center;
    .refresh {
      font-size: 14px;
      width: 14px;
      height: 14px;
      margin-right: 20px;
    }
    .ant-tag {
      cursor: pointer;
      .tag-num {
        color: @primary-color;
      }
    }
    .tag-table {
      .ant-table .ant-table-tbody td {
        line-height: 30px;
        height: 30px;
      }
    }
    .tag-apply {
      position: absolute;
      left: 16px;
      bottom: 27px;
    }
  }
  .chart-content {
    height: calc(100% - 43px);
    width: 100%;
  }

  canvas {
    padding: 7px;
  }
}

.ant-popover-title {
  font-size: 16px;
}
