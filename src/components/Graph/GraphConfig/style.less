.ant-modal-GraphConfig {
  .tagkvModal {
    font-size: 12px;
  }
  .ant-divider {
    margin: 0 4px;
    color: #999;
    display: inline-block;
    height: 8px;
    width: 1px;
    background: #ccc;
  }
  // fix ant style
  .ant-modal-header {
    // padding: 8px 12px;
  }
  .ant-modal-close {
    top: 22px;
  }
  .ant-modal-content {
    min-width: 637px;
  }
  .ant-modal-body {
    padding: 10px 20px 10px;
  }
  .ant-table-thead > tr > th, .ant-table-tbody > tr > td {
      padding: 3px 8px;
  }
  .ant-table-tbody > tr > td {
    border-bottom: 0 none;
  }
  .ant-table-thead > tr > th {
    font-weight: normal;
    color: #999;
  }
  .ant-table-middle .ant-table-thead > tr > th, .ant-table-middle .ant-table-tbody > tr > td {
    padding: 0px 10px;
    height: auto;
    line-height: 1.5;
  }
  .ant-table-middle .ant-table-thead > tr > th {
    padding: 6px 10px;
  }
  .ant-tabs.ant-tabs-card > .ant-tabs-bar .ant-tabs-tab-active .ant-tabs-tab-inner {
    padding: 3px 5px;
  }
  .ant-tabs.ant-tabs-card > .ant-tabs-bar .ant-tabs-tab-inner {
    padding: 3px 5px;
  }
  .ant-tabs.ant-tabs-card > .ant-tabs-bar .ant-tabs-tab {
    // border-radius: 0;
  }
  .ant-tabs.ant-tabs-card > .ant-tabs-bar .ant-tabs-nav-container {
    height: 28px;
  }
  .ant-tabs-bar {
    margin-bottom: 10px;
    margin-left: 15px;
    padding-left: 45px;
  }
  .ant-modal-close-x {
    height: 14px;
    font-size: 14px;
    line-height: 1;
  }
}

.graph-config-form-container {
  .small-tabs-card {
    margin-top: 5px;
  }
}

.graph-config-inner-comparison {
  .ant-select-dropdown {
    width: 80px !important;
  };
}
