.time-range-picker {
  .ant-popover-inner-content {
    padding: 0;
  }
  .time-range-picker-wrapper {
    width: fit-content;
    height: 244px;
    display: flex;
    .time-range-picker-left {
      width: 150px;
      height: 100%;
      padding-top: 5px;
      border-right: 1px solid #d7dbec;
      overflow: auto;
      .num {
        display: inline-block;
        width: 15px;
        text-align: center;
        margin: 0 4px;
      }
      button {
        color: #4c5273;
        padding-left: 20px;
        line-height: 20px;
        width: 100%;
        text-align: left;
        &:hover {
          color: @primary-color;
          background: rgba(20, 115, 255, 0.06);
        }
        &.active {
          color: @primary-color;
        }
      }
    }
    .time-range-picker-right {
      padding: 12px 20px;
      .title {
        color: #4c5273;
        font-weight: bold;
      }
      .ant-picker {
        margin-bottom: 16px;
      }
      .footer {
        display: flex;
        justify-content: flex-end;
        button {
          margin-left: 10px;
        }
      }
    }
  }
}

.time-range-picker-target-nullable {
  .anticon-close-circle {
    display: none;
  }
  &:hover {
    .anticon-caret-down {
      display: none;
    }
    .anticon-close-circle {
      display: inline-block;
    }
  }
}
