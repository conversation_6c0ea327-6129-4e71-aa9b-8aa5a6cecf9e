@import '@/global.variable.less';

.event-content {
  display: flex;
  padding: @basePadding;
  box-sizing: border-box;
  &.cur-events {
    .dantd-data-table-header-loadbtn-left {
      margin-right: 0px;
    }
  }
  .table-area {
    overflow: auto;
    padding: 16px;
    width: 100%;
    table {
      border-collapse: collapse;
      thead {
        border-left: 10px solid #fafafa;
      }
    }
    .event-tags {
      .ant-tag {
        cursor: pointer;
      }
    }
    .dantd-data-table-header-loadbtn-left {
      border-bottom-right-radius: 0px;
      border-top-right-radius: 0px;
    }
  }
  .table-operate-box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .left {
      flex: 1;
      display: flex;
      padding-right: 10px;
      overflow: hidden;
      .search-input {
        width: 100px;
        max-width: 300px;
        flex: 1;
        margin-left: 10px;
      }
    }
    .right {
      display: flex;
    }
    .interval-btn {
      border-bottom-left-radius: 0px;
      border-top-left-radius: 0px;
      border-left: 0px;
      border-color: #d9d9d9;
      &:hover {
        border-color: #d9d9d9;
      }
    }
  }
  .ant-table-row {
    position: relative;
  }

  .event-page-title {
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .card-menu-item {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    .label-area {
      flex-grow: 1;
    }
    .default-holder {
      width: 48px;
      text-align: right;
      color: rgb(204, 204, 204);
    }
    .icon-area {
      width: 48px;
      display: none;
      flex-shrink: 0;
      text-align: right;
    }
    &:hover {
      background-color: fade(@primary-color, 10%);
      .icon-area {
        display: block;
      }
      .default-holder {
        display: none;
      }
    }
    .title {
      word-break: break-all;
    }
    .desc {
      color: #8c8c8c;
      word-break: break-all;
    }
    .anticon {
      font-size: 14px;
      color: #8c8c8c;
      margin-left: 8px;
      &:hover {
        color: @primary-color;
      }
    }
  }
  .event-card {
    position: relative;
    padding: 16px;
    cursor: pointer;
    &-num {
      position: absolute;
      right: 16px;
      bottom: 10px;
      font-size: 36px;
      font-weight: bold;
    }
    &.yellow {
      color: #fec10b;
      background-color: #fef8e6;
    }
    &.orange {
      color: #f27f0d;
      background-color: #fdf2e6;
    }
    &.red {
      color: #e71408;
      background-color: #fce7e6;
    }
    height: 117px;
    border-radius: 3px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }
}

.yellow-left-border {
  border-left: 10px solid #fec10b;
}
.orange-left-border {
  border-left: 10px solid #f27f0d;
}
.red-left-border {
  border-left: 10px solid #e71408;
}
.green-left-border {
  border-left: 10px solid #52c41a;
}
.card-event-drawer {
  table {
    border-collapse: collapse;
    thead {
      border-left: 10px solid #fafafa;
    }
    td {
      line-height: 28px;
    }
  }
}
