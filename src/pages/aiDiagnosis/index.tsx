import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Modal, Form, Input, message, Space, Upload, Tooltip, Alert } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, DownloadOutlined, UploadOutlined, SyncOutlined } from '@ant-design/icons';
import { 
  getDiagnosisModules, 
  createDiagnosisModule, 
  updateDiagnosisModule, 
  deleteDiagnosisModule,
  exportDiagnosisModulesFile
} from '@/services/aiDiagnosis';
import DiagnosisResult from './components/DiagnosisResult';
import type { UploadProps } from 'antd';
import type { RcFile } from 'antd/es/upload';

const AiDiagnosis: React.FC = () => {
  const [modules, setModules] = useState<any[]>([]);
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [selectedModule, setSelectedModule] = useState<any>(null);
  const [showResult, setShowResult] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showExportTip, setShowExportTip] = useState(false);

  useEffect(() => {
    fetchModules();
    // 每30秒刷新一次，以获取可能的文件更新
    const intervalId = setInterval(fetchModules, 30000);
    return () => clearInterval(intervalId);
  }, []);

  const fetchModules = async () => {
    setLoading(true);
    try {
      const data = await getDiagnosisModules();
      setModules(data);
    } catch (error) {
      console.error('Failed to fetch modules:', error);
      message.error('加载诊断模块失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingId(null);
    form.resetFields();
    setVisible(true);
  };

  const handleEdit = (record: any) => {
    setEditingId(record.id);
    form.setFieldsValue({
      ...record,
      difyConfig: typeof record.difyConfig === 'string' 
        ? record.difyConfig 
        : JSON.stringify(record.difyConfig, null, 2)
    });
    setVisible(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteDiagnosisModule(id);
      message.success('删除成功，请替换服务器上的配置文件');
      setShowExportTip(true);
      fetchModules(); // 刷新列表时仍显示当前状态，因为实际文件未更新
    } catch (error) {
      message.error('删除失败');
    }
  };

  const validateDifyConfig = (config: string) => {
    try {
      const parsedConfig = JSON.parse(config);
      if (!parsedConfig.apiKey || !parsedConfig.endpoint) {
        throw new Error('API Key 和 Endpoint 为必填项');
      }
      return true;
    } catch (error) {
      throw new Error('Dify API 配置格式不正确');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      // 确保 difyConfig 是有效的 JSON 字符串
      let difyConfig;
      try {
        difyConfig = typeof values.difyConfig === 'string' 
          ? JSON.parse(values.difyConfig)
          : values.difyConfig;
      } catch (e) {
        throw new Error('Dify 配置格式不正确，请检查 JSON 格式');
      }

      // 验证必要字段
      if (!difyConfig.apiKey) {
        throw new Error('请填写 API Key');
      }
      if (!difyConfig.endpoint) {
        throw new Error('请填写 Endpoint');
      }
      if (!difyConfig.workflowId) {
        throw new Error('请填写 Workflow ID');
      }

      // 确保 endpoint 格式正确
      if (!difyConfig.endpoint.startsWith('http')) {
        difyConfig.endpoint = `http://${difyConfig.endpoint}`;
      }

      // 移除 endpoint 末尾的斜杠
      difyConfig.endpoint = difyConfig.endpoint.replace(/\/+$/, '');

      // 更新配置
      values.difyConfig = JSON.stringify(difyConfig);
      
      if (editingId) {
        await updateDiagnosisModule(editingId, values);
        message.success('更新成功，请替换服务器上的配置文件');
      } else {
        await createDiagnosisModule(values);
        message.success('创建成功，请替换服务器上的配置文件');
      }
      setVisible(false);
      setShowExportTip(true);
      fetchModules(); // 刷新列表以保持数据一致性
    } catch (error) {
      message.error('操作失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
  };

  const handleRunDiagnosis = (module: any) => {
    setSelectedModule(module);
    setShowResult(true);
  };

  const handleExportConfig = () => {
    exportDiagnosisModulesFile(modules);
    message.success('配置文件已导出，请替换服务器上的配置文件');
  };

  const handleImportConfig = async (file: RcFile) => {
    try {
      const fileContent = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsText(file);
      });
      
      const importedModules = JSON.parse(fileContent);
      
      if (!Array.isArray(importedModules)) {
        throw new Error('导入的配置格式不正确，应为数组');
      }
      
      // 验证每个模块的格式
      importedModules.forEach((module, index) => {
        if (!module.id || !module.name || !module.difyConfig) {
          throw new Error(`第 ${index + 1} 个模块缺少必要字段 (id, name, difyConfig)`);
        }
      });
      
      // 设置当前状态并导出文件
      setModules(importedModules);
      exportDiagnosisModulesFile(importedModules);
      message.success('配置导入成功，请替换服务器上的配置文件');
      setShowExportTip(true);
      
    } catch (error) {
      message.error('导入失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }
    
    return false; // 防止自动上传
  };

  const uploadProps: UploadProps = {
    name: 'file',
    accept: '.json',
    showUploadList: false,
    beforeUpload: handleImportConfig,
  };

  const columns = [
    {
      title: '模块名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Dify API 配置',
      dataIndex: 'difyConfig',
      key: 'difyConfig',
      render: (text: string, record: any) => {
        try {
          const config = typeof text === 'string' ? JSON.parse(text) : text;
          return `${config.endpoint} (${config.workflowId ? '工作流' : config.agentId ? '智能体' : '基础调用'})`;
        } catch {
          return '配置格式错误';
        }
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Space>
          <Button type="link" onClick={() => handleRunDiagnosis(record)}>
            运行诊断
          </Button>
          <Button type="link" icon={<EditOutlined />} onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Button type="link" danger icon={<DeleteOutlined />} onClick={() => handleDelete(record.id)}>
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {showExportTip && (
        <Alert
          type="warning"
          message="配置文件更新提示"
          description={
            <div>
              <p>您已对配置进行了修改，但这些修改只在当前浏览器中有效。</p>
              <p>请点击"导出配置"按钮下载最新的配置文件，并替换服务器上的 
                <code>/public/defaults/diagnosis-modules.json</code> 文件，以使修改永久生效。
              </p>
              <Button type="primary" size="small" onClick={handleExportConfig} style={{ marginRight: '10px' }}>
                导出配置
              </Button>
              <Button size="small" onClick={() => setShowExportTip(false)}>
                稍后处理
              </Button>
            </div>
          }
          closable
          onClose={() => setShowExportTip(false)}
          style={{ marginBottom: '16px' }}
        />
      )}
      
      <Card
        title="智能诊断"
        extra={
          <Space>
            <Tooltip title="刷新模块列表">
              <Button 
                icon={<SyncOutlined />} 
                onClick={fetchModules}
                loading={loading}
              />
            </Tooltip>
            <Tooltip title="导出配置">
              <Button 
                icon={<DownloadOutlined />} 
                onClick={handleExportConfig}
              />
            </Tooltip>
            <Tooltip title="导入配置">
              <Upload {...uploadProps}>
                <Button icon={<UploadOutlined />} />
              </Upload>
            </Tooltip>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              添加诊断模块
            </Button>
          </Space>
        }
      >
        <Table 
          columns={columns} 
          dataSource={modules} 
          rowKey="id" 
          loading={loading}
          locale={{ emptyText: '暂无配置，请添加诊断模块或导入配置文件' }}
        />
      </Card>

      <Modal
        title={editingId ? '编辑诊断模块' : '添加诊断模块'}
        visible={visible}
        onOk={handleSubmit}
        onCancel={() => setVisible(false)}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="模块名称"
            rules={[{ required: true, message: '请输入模块名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item name="description" label="描述">
            <Input.TextArea />
          </Form.Item>
          <Form.Item
            name="difyConfig"
            label="Dify API 配置"
            rules={[
              { required: true, message: '请输入 Dify API 配置' },
              { validator: (_, value) => {
                if (value) {
                  try {
                    validateDifyConfig(value);
                    return Promise.resolve();
                  } catch (error) {
                    return Promise.reject(error instanceof Error ? error.message : '验证失败');
                  }
                }
                return Promise.resolve();
              }}
            ]}
          >
            <Input.TextArea
              placeholder={`请输入 JSON 格式的 Dify API 配置，例如：
{
  "apiKey": "your-api-key",
  "endpoint": "https://api.dify.ai/v1/chat-messages",
  "workflowId": "optional-workflow-id",
  "agentId": "optional-agent-id"
}`}
              rows={8}
            />
          </Form.Item>
          <Alert
            type="info"
            message="配置文件更新说明"
            description="修改配置后，系统会导出配置文件。您需要将此文件替换服务器上的配置文件，以使修改永久生效。"
            style={{ marginBottom: '16px' }}
          />
        </Form>
      </Modal>

      {showResult && selectedModule && (
        <DiagnosisResult
          visible={showResult}
          onClose={() => setShowResult(false)}
          module={selectedModule}
        />
      )}
    </div>
  );
};

export default AiDiagnosis; 