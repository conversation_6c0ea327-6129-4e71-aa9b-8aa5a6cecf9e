import React, { useState, useEffect } from 'react';
import { Modal, Spin, message, Typography } from 'antd';
import { runDiagnosis } from '@/services/aiDiagnosis';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import '@/styles/diagnostic-result.css';

const { Text } = Typography;

interface DiagnosisResultProps {
  visible: boolean;
  onClose: () => void;
  module: any;
}

const DiagnosisResult: React.FC<DiagnosisResultProps> = ({ visible, onClose, module }) => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (visible && module) {
      handleRunDiagnosis();
    }
  }, [visible, module]);

  const handleRunDiagnosis = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await runDiagnosis(module);
      if (response.event === 'workflow_finished') {
        setResult(response.data);
      } else {
        setResult(response);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '诊断执行失败';
      setError(errorMessage);
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const renderContent = () => {
    if (loading) {
      return (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>诊断分析中，请稍候...</div>
        </div>
      );
    }

    if (error) {
      return (
        <div style={{ color: '#ff4d4f', padding: '20px', textAlign: 'center' }}>
          <Text type="danger" style={{ fontSize: '16px' }}>{error}</Text>
          <div style={{ marginTop: '20px' }}>
            <button 
              onClick={handleRunDiagnosis}
              style={{
                background: '#1890ff',
                color: 'white',
                border: 'none',
                padding: '8px 16px',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              重试
            </button>
          </div>
        </div>
      );
    }

    if (!result) {
      return <div style={{ textAlign: 'center', padding: '20px' }}>暂无诊断结果</div>;
    }

    // 提取诊断结果文本
    const diagnosticText = result.outputs?.text || JSON.stringify(result, null, 2);

    return (
      <div className="diagnostic-result">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          components={{
            // 自定义表格样式
            table: ({ children }) => (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  {children}
                </table>
              </div>
            ),
            th: ({ children }) => (
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {children}
              </th>
            ),
            td: ({ children }) => (
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {children}
              </td>
            ),
            // 自定义 details 标签样式
            details: ({ children }) => (
              <details className="diagnostic-details">
                {children}
              </details>
            ),
            summary: ({ children }) => (
              <summary className="diagnostic-summary">
                {children}
              </summary>
            ),
            // 自定义代码块样式
            code: ({ node, inline, className, children, ...props }) => {
              const match = /language-(\w+)/.exec(className || '');
              return !inline && match ? (
                <pre className="diagnostic-code-block">
                  <code className={className} {...props}>
                    {children}
                  </code>
                </pre>
              ) : (
                <code className="diagnostic-inline-code" {...props}>
                  {children}
                </code>
              );
            },
            // 自定义引用块样式
            blockquote: ({ children }) => (
              <blockquote className="diagnostic-blockquote">
                {children}
              </blockquote>
            ),
            // 自定义分割线样式
            hr: () => <hr className="diagnostic-hr" />,
          }}
        >
          {diagnosticText}
        </ReactMarkdown>
      </div>
    );
  };

  return (
    <Modal
      title={`${module.name} - 诊断结果`}
      visible={visible}
      onCancel={onClose}
      width={800}
      footer={null}
      destroyOnClose
    >
      {renderContent()}
    </Modal>
  );
};

export default DiagnosisResult; 