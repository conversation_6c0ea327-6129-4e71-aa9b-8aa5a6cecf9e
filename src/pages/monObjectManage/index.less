.object-manage-page-content {
  display: flex;
  padding: @basePadding;
  overflow-x: auto;
  box-sizing: border-box;
  .table-area {
    // width: calc(100% - 210px);
    padding: 16px;
    flex: 1;
    .table-operate-box {
      width: 100%;
      display: flex;
      justify-content: space-between;
      .search-input {
        width: 300px;
      }
    }
    .ant-tag {
      cursor: pointer;
    }
    .table-td-fullBG {
      height: 100%;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    th,td {
      text-align: center;
    }
    td {
      padding: 1px !important;
      line-height: unset !important;
    }
  }
  
}

.mon-manage-table-tooltip {
  .ant-tag {
    margin: 2px 4px;
    cursor: pointer;
  }
}
