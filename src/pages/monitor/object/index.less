.object-view {
  .host-select {
    padding-bottom: 8px;
    .top-bar {
      display: flex;
      .host-input {
        height: 32px;
        .ant-input-group-addon {
          padding: 0;
          background: #fff;
          border-radius: 0;
          .host-add-on {
            display: flex;
            .title {
              width: 71px;
              height: 30px;
              line-height: 30px;
              border-right: 1px solid #d9d9d9;
            }
            .select-before {
              width: 112px;
              height: 30px;
              line-height: 30px;
            }
          }
        }
        .ant-input {
          padding-left: 24px;
          border-radius: 0;
        }
      }
    }
    .host-list {
      border: 1px solid #d9d9d9;
      border-top: none;
      max-height: 300px;
      overflow: auto;
      .ant-table {
        overflow: auto;
        margin-top: 0;
        height: 250px;
        border-left: none;
        border-right: none;
        border-top: none;
        border-radius: 0;
      }
      .ant-pagination {
        margin: 12px 0;
        padding-right: 8px;
      }
    }
    .host-list-empty {
      .ant-table {
        margin-top: 0;
        height: 299px;
        border-top: none;
        border-radius: 0;
      }
    }
  }
  .metric-select {
    .top-bar {
      display: flex;
      .metric-search {
        .ant-input-group-addon {
          border-radius: 0;
          background-color: #fff;
          .ant-input-search-button {
            border-radius: 0;
          }
        }
        .ant-input {
          padding-left: 24px;
        }
      }
    }
    .metric-tab {
      padding: 8px;
      border: 1px solid #d9d9d9;
      border-top: 0;
      padding-top: 0;
    }
    .metric-tab-empty {
      height: 299px;
      border: 1px solid #d9d9d9;
      border-top: 0;
      line-height: 299px;
      text-align: center;
    }
    .metric-list {
      list-style: none;
      padding: 0;
      margin-bottom: 0;
      .item {
        height: 24px;
        cursor: pointer;
      }
    }
  }
  .empty-graph {
    height: 300px;
    border: 1px solid #d9d9d9;
    line-height: 300px;
    text-align: center;
  }
}
