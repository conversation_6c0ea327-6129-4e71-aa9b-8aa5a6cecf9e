@margin-base: 10px;
.metric-page-title {
  font-size: 14px;
  font-weight: 700;
  display: flex;
  align-items: center;
}

.n9e-metric-views {
  // width: 100%;
  display: flex;
  gap: 10px;
  .mt16 {
    margin-top: @margin-base;
  }
  .mb16 {
    margin-bottom: @margin-base;
  }
  .page-title {
    font-weight: 700;
    font-size: 14px;
    height: unset;
  }
}
.n9e-metric-views-list {
  flex: 0 0 240px;
  padding: @margin-base;
}
.n9e-metric-views-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: @margin-base;
  margin-top: 6px; // 为了对齐
}
.n9e-metric-views-list-content {
  margin-top: @margin-base;
}
.n9e-metric-views-list-content-item {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  position: relative;
  cursor: pointer;
  .anticon {
    margin-left: 8px;
    display: none;
  }
  &:hover {
    .anticon {
      display: inline;
    }
    .n9e-metric-views-list-content-item-cate {
      display: none;
    }
  }
  &.active {
    background-color: fade(@primary-color, 10%);
  }
}
.n9e-metric-views-labels-values {
  flex: 0 0 240px;
  padding: @margin-base;
  overflow-y: auto;
}
.n9e-metric-views-labels-values-item {
  .page-title {
    margin-top: 20px;
  }
  &:first-child {
    .page-title {
      margin-top: 6px;
    }
  }
}
.n9e-metric-views-filters {
  margin: @margin-base 0;
  word-break: break-all;
}
.n9e-metric-views-dynamicLabels {
  margin-top: @margin-base;
}
.n9e-metric-views-dynamicLabels-item {
  margin-bottom: @margin-base;
}
.n9e-metric-views-dynamicLabels-item-label {
  margin-bottom: 8px;
}
.n9e-metric-views-dimensionLabel {
  margin-top: @margin-base;
}
.n9e-metric-views-dimensionLabel-content {
  margin-top: @margin-base;
}
.n9e-metric-views-dimensionLabel-content-item {
  padding: 6px;
  margin-bottom: 2px;
  cursor: pointer;
  &.active {
    background-color: fade(@primary-color, 10%);
  }
}
.n9e-metric-views-metrics {
  width: calc(~'100% - 500px');
  overflow-y: auto;
  padding: @margin-base;
  .ant-tabs-large > .ant-tabs-nav .ant-tabs-tab {
    font-size: 12px;
    padding: 10px 0;
  }
  .ant-tabs-tab + .ant-tabs-tab {
    margin-left: 20px;
  }
}
.n9e-metric-views-metrics-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  .ant-input-group-wrapper {
    width: 50%;
  }
}
.n9e-metric-views-metrics-content {
  list-style: none;
  margin: 0;
  padding: 0;
  overflow: hidden;
  .item {
    height: 24px;
    line-height: 24px;
    cursor: pointer;
    display: flex;
  }
  .desc {
    padding-left: 20px;
    color: #999;
    white-space: nowrap;
  }
}
.n9e-metric-views-modal {
  .ant-modal-header {
    padding-bottom: 0px;
  }
  .custom-import-title {
    .ant-tabs-nav {
      margin: 0;
    }
  }
}
.n9e-metric-views-metrics-graph {
  .button-link-icon {
    color: #999;
    cursor: pointer;
    &:hover {
      color: @primary-color;
    }
  }
  .button-link-icon.active {
    color: @primary-color;
    cursor: unset;
  }

  .a-icon {
    color: #999;
  }
}
