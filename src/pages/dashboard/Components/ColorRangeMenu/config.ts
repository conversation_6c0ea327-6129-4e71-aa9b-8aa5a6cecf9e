/*
 * Copyright 2022 Nightingale Team
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
export const colors = [
  {
    label: 'green / orange',
    value: ['#83c898', '#c2c2c2', '#fc653f'],
  },
  {
    label: 'yellow / green',
    value: ['#f0ee6e', '#6ba261', '#306d52'],
  },
  {
    label: 'warm',
    value: ['#ffeda0', '#fc4e2a', '#800026'],
  },
  {
    label: 'cool',
    value: ['#c7f1ff', '#42a1fa', '#083294'],
  },
  {
    label: 'plasma',
    value: ['#f0f921', '#b12a90', '#0d0887'],
  },
  {
    label: 'viridis',
    value: ['#fde725', '#2a788e', '#440154'],
  },
];