.renderer-container {
  height: 100%;
  background-color: #fff;
  box-shadow: 0px 0px 10px 2px rgba(0,0,0,0.04);
  &:hover {
    .renderer-header-desc {
      .anticon-info-circle,
      .anticon-link {
        display: inline;
      }
    }
    .renderer-header-loading {
      .renderer-header-more {
        display: inline;
      }
    }
  }
  .ant-tooltip-inner {
    min-height: 26px !important;
  }
}
.renderer-container-no-title {
  .renderer-header {
    position: absolute;
    z-index: 1;
    width: 100%;
  }
}
.renderer-header {
  padding: 0;
  height: 46px;
  line-height: 46px;
  display: flex;
  border: 0 none !important;
  background-color: unset;
}
.renderer-header-desc {
  width: 34px;
  text-align: center;
  cursor: pointer;
  position: relative;
  .anticon-info-circle,
  .anticon-link {
    display: none;
  }
}
.renderer-header-content {
  text-align: center;
  flex: 1;
  width: calc(100% - 68px);
  .renderer-header-title {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    font-size: 14px;
  }
}
.renderer-header-loading {
  width: 34px;
  text-align: center;
  .renderer-header-more {
    display: none;
  }
}
.renderer-body-wrap {
  height: 100%;
}
.renderer-body {
  .unknown-type {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
}

.renderer-body-content-empty {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
}