.n9e-collapse {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: #333;
  font-size: 12px;
  font-variant: tabular-nums;
  line-height: 1.5715;
  list-style: none;
  font-feature-settings: 'tnum';
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-bottom: 0;
  border-radius: 2px;
}

.n9e-collapse .n9e-collapse-item:last-child,
.n9e-collapse .n9e-collapse-item:last-child .n9e-collapse-header {
  border-radius: 0 0 2px 2px;
}
.n9e-collapse .n9e-collapse-item {
  border-bottom: 1px solid #d9d9d9;
}

.n9e-collapse .n9e-collapse-item .n9e-collapse-header {
  display: flex;
  position: relative;
  padding: 12px 16px;
  padding-left: 40px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5715;
  cursor: pointer;
  transition: all 0.3s, visibility 0s;
}
.n9e-collapse .n9e-collapse-item .n9e-collapse-header .n9e-collapse-extra {
  margin-left: auto;
}
.n9e-collapse .n9e-collapse-item .n9e-collapse-header .n9e-collapse-arrow {
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: absolute;
  top: 3.429px;
  left: 16px;
  display: inline-block;
  padding: 12px 16px;
  padding-right: 0;
  padding-bottom: 0;
  padding-left: 0;
  font-size: 12px;
}

.n9e-collapse-item:last-child .n9e-collapse-content {
  border-radius: 0 0 2px 2px;
}
.n9e-collapse-content {
  color: #333;
  background-color: #fff;
  border-top: 1px solid #d9d9d9;
}

.n9e-collapse-content .n9e-collapse-content-box {
  padding: 16px;
}

.n9e-collapse-content-hidden {
  display: none;
}

.n9e-collapse-item-inner {
  .n9e-collapse-item {
    background-color: #f9f9f9;
    margin-bottom: 5px;
    border: 0 none;
    .n9e-collapse-content {
      border: 0 none;
      background-color: unset;
    }
  }
}
