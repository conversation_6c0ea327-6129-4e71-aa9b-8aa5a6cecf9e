@backgroupColor: #20222E;
@baseColor: #F7F8F2;
@cardBackgroundColor: #2A2D3C;
.theme-dark {
  background-color: @backgroupColor;
  color: @baseColor;
  
  .ant-table .ant-table-tbody td {
    border-color: #303030;
    color: unset;
  }
  .ant-table .ant-table-thead th {
    color: unset;
  }
  .dashboard-detail-header-left {
    .title {
      color: #fefefe;
    }
  }
  .dashboard-detail-content {
    background-color: @backgroupColor !important;
  }
  .dashboards-panels-row {
    background-color: unset;
  }
  .renderer-container {
    background-color: @cardBackgroundColor;
  }
  .tag-content-close-item-tagName {
    color: rgba(255, 255, 255, 0.85);
    background-color: rgba(255, 255, 255, 0.04);
  }
  .renderer-timeseries-legend-list,.renderer-timeseries-legend-toggle {
    background-color: #323545;
  }
  .renderer-timeseries-legend-list {
    & > div.disabled {
      color: #74778A
    }
  }
  .react-grid-item > .react-resizable-handle::after{
    border-right: 2px solid #46495C;
    border-bottom: 2px solid #46495C;
  }

  .scroll-container {
  }
  .scroll-container::-webkit-scrollbar {
    height: 10px;
    width: 4px;
  }
  .scroll-container::-webkit-scrollbar-corner {
    background-color: transparent;
  }
  .scroll-container::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #404354;
  }
  .scroll-container::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    background: unset;
  }
}