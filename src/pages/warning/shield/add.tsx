/*
 * Copyright 2022 Nightingale Team
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import PageLayout from '@/components/pageLayout';
import OperateForm from './components/operateForm';
import { useTranslation } from 'react-i18next';
import './index.less';

const AddShield: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  let tags: any = location.state || {};
  if (tags) {
    tags.cate = tags.cate || 'prometheus';
  }

  return (
    <PageLayout title={t('告警屏蔽')} showBack hideCluster>
      <div className='shield-add'>
        <OperateForm tagsObj={tags ? tags : undefined} />
      </div>
    </PageLayout>
  );
};

export default AddShield;
