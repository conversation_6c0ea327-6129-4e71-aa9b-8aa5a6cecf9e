/*
 * Copyright 2022 Nightingale Team
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
import React, { useMemo, useEffect, useState } from 'react';
import { useHistory, useParams, useLocation } from 'react-router-dom';
import PageLayout from '@/components/pageLayout';
import OperateForm from './components/operateForm';
import { useTranslation } from 'react-i18next';
import { getWarningStrategy } from '@/services/warning';
import { useQuery } from '@/utils';

import './index.less';

const StrategyEdit: React.FC = () => {
  const { t } = useTranslation();
  const query = useQuery();
  const isClone = query.get('mode');
  const params: any = useParams();
  const strategyId = useMemo(() => {
    return params.id;
  }, [params]);
  const [curStrategy, setCurStrategy] = useState<any>({});
  useEffect(() => {
    getStrategy();
    return () => {};
  }, [strategyId]);

  const getStrategy = async () => {
    const res = await getWarningStrategy(strategyId);
    setCurStrategy(res.dat || {});
  };

  return (
    <PageLayout title={t('告警规则')} showBack backPath='/monitor/alert-rules' hideCluster>
      {curStrategy.id && <OperateForm detail={curStrategy} type={!isClone ? 1 : 2} />}
    </PageLayout>
  );
};

export default StrategyEdit;
