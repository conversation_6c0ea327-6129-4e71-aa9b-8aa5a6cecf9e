.shield-content {
  display: flex;
  flex-direction: row;
  padding: @basePadding;
  overflow-x: auto;
  box-sizing: border-box;
}
.anticon-question-circle {
  color: @primary-color;
}
.shield-add {
  .ant-picker {
    width: 100%;
  }
}
.shield-index {
  flex: 1;
  .header {
    display: flex;
    padding: 10px 10px 0;
    justify-content: space-between;
    .searchInput {
      margin-left: 10px;
    }
  }
  .ant-table-wrapper {
    padding: 0px 10px 10px;
  }
  .ant-popover {
    .ant-popover-inner-content {
      padding: 6px 8px;
    }
  }
  .dismiss-confirm {
    .ant-popover-content {
      width: 200px;
    }
  }
  .shield-time {
    div {
      padding-top: 1px;
      padding-bottom: 1px;
      line-height: 16px;
    }
  }
}

.operate-form-index {
  .ant-card-body {
    padding-top: 40px;
  }
}

.strategy-form {
  .ant-checkbox-wrapper {
    display: inline-flex;
    align-items: flex-start;
  }
}

.rule_modal_table {
  .ant-table {
    overflow-x: auto;
    overflow-y: hidden;
    .ant-table-tbody {
      td {
        line-height: 1.8;
      }
    }
  }
}
