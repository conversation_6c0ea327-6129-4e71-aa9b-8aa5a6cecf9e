.explore {
  height: 100%;
  .left {
    &:before {
      content: '';
      display: block;
      position: absolute;
      top: 0;
      bottom: 0;
      right: 0;
      width: 16px;
      margin-left: 0;
      background: #f6f5f6;
      background-image: linear-gradient(90deg, rgba(0, 0, 0, 0.08) 0, #f6f5f6 10px);
      z-index: 2;
    }
    width: 100%;
    padding: 10px 25px 10px 10px;
    display: flex;
    flex-direction: column;
    // .page-title {
    //   font-size: @baseFontSizeMiddle;
    //   color: black;
    //   font-weight: bold;
    //   .anticon {
    //     margin-right: 10px;
    //   }
    // }
    .panel {
      padding: 8px;
      background: @panelBackground;
      border-radius: 4px;
      margin-bottom: 25px;
      .title {
        margin-bottom: 5px;
        font-size: 14px;
      }
      .echart-num {
        margin-left: 5px;
        margin-right: 5px;
      }
      .echart-line-num {
        margin-left: 5px;
        margin-right: 5px;
      }
      .sel-metric {
        height: 60px;
        p {
          height: 20px;
          margin: 0px;
          &.desc {
            color: @colorGray3;
          }
        }
      }
    }
  }
  .right {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #f6f5f6;
    .header {
      display: flex;
      padding: 16px;
      background: #fff;
      border-bottom: 1px solid var(--ui-border);
      justify-content: space-between;
      &-left {
        width: 360px;
        display: flex;
      }
    }
    .chart-list {
      flex: 1;
      overflow: auto;
      padding-right: 16px;
      .ant-col {
        margin-top: 15px;
        height: 400px;
      }
      .holder {
        width: 100%;
        height: 100px;
        justify-content: center;
        align-items: center;
        display: flex;
        border: 2px solid #e0dee2;
        margin: 10px;
        background: #fff;
        color: @primary-color;
        & > span {
          margin-left: 5px;
        }
      }
    }
  }
  .ant-row {
    height: 100%;
  }
}
