.chart-container {
  padding: 20px;
  &-header {
    display: flex;
    margin-bottom: 10px;
  }
  .holder {
    width: 100%;
    height: 100px;
    justify-content: center;
    align-items: center;
    display: flex;
    border: 2px solid #e0dee2;
    margin: 10px;
    background: #fff;
    & > span {
      margin-left: 5px;
    }
  }
  .chart-container-header {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
    }
  }
}
