@colorWhite: #fff;
@colorBlack: #000;
@colorGray1: #333;
@colorGray2: #f0f0f0;
@colorGray3: #999;
@colorGray4: #666;
@colorGray5: #f6f5f6;
@colorGray6: #f3f6f9;
@colorGray7: #f5f6fa;
@colorRed1: #ff5656;
@colorRed2: #f53146;
@colorRed3: #fdebed;
@colorBlue: #1473ff;
@colorLightBlue: #d4ecf9;
@colorDarkBlue: #005fb6;
@colorOrange: #fed738;
@colorWarning: #e6a23c;
@colorDanger: #ff5656;
@colorDangerBackground: @colorRed3;
@colorPrimary: @primary-color; // 品牌色
@baseFontSizeTiny: 12px;
@baseFontSizeSmall: 14px;
@baseFontSizeMiddle: 18px;
@baseFontSizeLarge: 24px;

@secondColor: #8c8c8c;

@fontColorLevel1: @colorBlack;
@fontColorLevel2: @colorGray1;
@fontColorLevel3: rgba(0, 0, 0, 0.85);
@fontColorLevel4: rgba(0, 0, 0, 0.65);
@fontColorBlue: @primary-color;
@fontColorWarning: @colorWarning;
@fontColorDanger: @colorDanger;

@basePadding: 12px 24px 0 24px;

@baseBorderColor: #d2d6dc;

// 背景色
@baseContentBgColor: @colorGray6;
@baseTableHeaderBgColor: @colorGray7;
@baseContentNormalBgColor: @colorGray7;

// 分割线
@baseDividerColor: @colorGray6;

@baseTransition: transform 0.15s cubic-bezier(0.55, 0, 0.1, 1);
@panelBackground: #f6f5f6;
@fcBorderColor: #e5e5e5;

.boxShadowMixin {
  box-shadow: 0px 1px 8px 0 rgba(0, 0, 0, 0.1);
}
.borderRadiusMixin {
  border-radius: 4px;
}
