{"新建资源分组": "新建资源分组", "端口号": "端口号", "classpath": {"single": "资源分组"}, "append_tags": "修改附加标签", "strategy": {"create": "新建策略分组", "single": "告警策略", "multiple": "告警策略"}, "personalInfo": "个人信息", "day-events": "天未恢复的", "n9e": "滴滴夜莺", "周期内所有点": "周期内所有点 - all", "发生次数": "发生次数 - happen", "最大值": "最大值 - max", "最小值": "最小值 - min", "均值": "均值 - avg", "求和": "求和 - sum", "突增突降值": "突增突降值 - diff", "突增突降率": "突增突降率 - pdiff", "同比变化率": "同比变化率 - c_avg_rate_abs", "同比变化率(区分正负)": "同比变化率(区分正负) - c_avg_rate", "同比变化值": "同比变化值 - c_avg_abs", "同比变化值(区分正负)": "同比变化值(区分正负) - c_avg", "3-sigma离群点检测": "3-sigma离群点检测 - stddev", "minute": "分钟", "minutes": "分钟", "hour": "小时", "hours": "小时", "tpl.create": "创建", "tpl.tag.bind": "绑定标签", "tpl.tag.unbind": "解绑标签", "tpl.tag.bind.title": "批量绑定标签", "tpl.tag.bind.field": "待绑定的标签", "tpl.tag.bind.success": "批量绑定标签成功", "tpl.tag.unbind.title": "批量解绑标签", "tpl.tag.unbind.field": "待解绑的标签", "tpl.tag.unbind.success": "批量解绑标签成功", "tpl.node.modify": "修改节点", "tpl.node.modify.title": "批量修改节点", "tpl.batch.modify.group": "批量修改所属分组", "tpl.title": "标题", "tpl.title.tpl.help": "标题，说明这个脚本的作用", "tpl.title.task.help": "标题，说明这个任务的作用", "tpl.tags": "标签", "tpl.tags.help": "标签，用于分类", "tpl.creator": "创建人", "tpl.last_updator": "更新人", "tpl.last_updated": "更新时间", "tpl.account.help": "执行账号，慎用root，除非要代表操作系统意志", "tpl.batch.help": "并发度，默认是0，表示全并发执行，1表示顺序执行，2表示每次执行2台", "tpl.tolerance.help": "容忍几台机器失败，默认是0，表示一台都不容忍，只要失败了，立马暂停", "tpl.timeout.help": "单机脚本执行的超时时间，单位是秒", "tpl.pause.help": "暂停点，做完某台之后暂停一下，要暂停多台，写多个 host，逗号分隔", "tpl.host.help": "要执行的机器列表", "tpl.script.help": "要执行的脚本内容", "tpl.args.help": "附于脚本之后的参数，多个参数之间用双逗号,,分隔，比如arg1,,arg2,,arg3", "tpl.modify": "编辑脚本", "tpl.create.task": "创建任务", "task.create": "创建任务", "task.title": "标题", "task.done": "是否完成", "task.clone": "克隆任务", "task.meta": "元信息", "task.creator": "创建人", "task.created": "创建时间", "task.only.mine": "只看自己", "task.host": "Host", "task.status": "状态", "task.output": "输出", "task.refresh": "刷新", "task.control.params": "控制参数", "task.account": "执行账号", "task.batch": "并发度", "task.tolerance": "容忍度", "task.timeout": "超时时间", "task.script": "脚本内容", "task.script.args": "脚本参数", "task.pause": "暂停点", "task.host.list": "机器列表", "task.clone.new": "克隆一个新任务", "task.temporary.create": "创建临时任务", "groups.instGroups": "即时任务", "groups.crontabGroups": "定时任务", "perms.hosts": "机器", "perms.host": "机器", "perms.team": "有权限的团队", "perms.account": "脚本执行账号", "last.7.days": "最近 7 天", "last.15.days": "最近 15 天", "last.30.days": "最近 30 天", "last.60.days": "最近 60 天", "last.90.days": "最近 90 天", "table.nodata": "暂无数据", "table.create": "创建", "table.operations": "操作", "table.batch.operations": "批量操作", "table.detail": "详情", "table.modify": "修改", "table.delete": "删除", "table.delete.batch": "批量删除", "table.clone": "克隆", "table.delete.sure": "确定要删除吗？", "table.delete.there.sure": "确定要删除这些吗?", "table.ident": "英文标识", "table.name": "显示名", "table.cate": "类别", "table.creator": "创建者", "table.lastupdated": "修改时间", "table.note": "备注", "form.submit": "提 交", "form.save": "保 存", "form.submit.and.save": "提交保存", "form.login": "登 录", "form.goback": "返 回", "msg.submit.success": "提交成功", "msg.modify.success": "修改成功", "msg.create.success": "创建成功", "msg.add.success": "添加成功", "msg.delete.success": "删除成功", "msg.clone.success": "克隆成功", "msg.clone.error": "克隆失败", "msg.sort.success": "排序成功"}