{"classpath": {"single": "Classpath"}, "strategy": {"create": "New", "single": "<PERSON><PERSON>", "multiple": "Alert <PERSON>"}, "personalInfo": "Profile", "n9e": "<PERSON><PERSON><PERSON>", "输入有误": "Incorrect input", "修改密码成功": "Changed the password successfully", "旧密码": "Old password", "请输入旧密码": "Enter the old password", "新密码": "New password", "请输入新密码": "Enter a new password", "再次输入新密码": "Enter a new password again", "确认修改": "Update", "自定义头像需以http开头": "The custom portrait needs to start at http", "自定义头像": "Custom portrait", "信息保存成功": "Saved successfully", "用户名": "Username", "显示名": "Display name", "请输入显示名": "Enter a display name", "邮箱": "Email", "请输入邮箱": "Enter email", "手机": "Mobile", "请输入手机号": "Enter mobile number", "请输入": "Enter ", "更多联系方式": "More contacts", "联系方式不能为空": "Contact information can not be blank", "请选择联系方式": "Select contact", "值不能为空": "Value can not be blank", "请输入值": "Enter value", "更换头像": "Change portrait", "头像URL": "Portrait URL", "个人中心": "Personal Center", "个人信息": "Setting", "修改密码": "Change password", "秘钥管理": "Token management", "Token更新成功": "Token updated successfully", "Token创建成功": "<PERSON>ken created successfully", "操作": "Action", "重新生成": "Regenerate", "生成": "Generate", "该分享链接无图表数据": "The shared link has no graph data.", "任意标签/资源分组": "Any Label / Classpath", "必须要有一个变量名": "Var name required", "变量名重复": "Duplicate variable name", "变量名包含非法字符": "The variable name contains illegal characters", "资源分组路径含有非法字符": "The Classpath contains illegal characters", "变量名": "Variable name", "标签或资源分组": "Label or Classpath", "默认值": "Default value", "添加大盘变量": "Add dashboard variables", "添加变量": "Add variable", "完成": "Finish", "新建图表": "New chart", "标题": "Title", "图表名称": "Name required", "配置方式": "Mode", "指标": "Metrics", "请选择指标": "Select metrics", "请输入监控指标(输入4个以上字符发起检索)": "Enter a metric(search after 4 char)", "资源分组": "Classpaths", "请选择资源分组": "Select classpath", "前缀匹配": "Prefix", "请输入Promql": "Enter PromQL", "阈值": "<PERSON><PERSON><PERSON><PERSON>", "下钻链接": "Drill URL", "列": " Column", "新增图表": "New Chart", "一键规整": "<PERSON><PERSON><PERSON>", "修改": "Edit", "上移": "Move up", "下移": "Move down", "是否删除分类": "Delete group", "删除": "Delete", "编辑图表": "Edit Chart", "是否删除图表": "Delete the chart", "删除图表": "Delete", "删除分组成功": "Group deleted successfully", "新增图表分组": "New Chart Group", "新建分组": "New Chart Group", "更新分组名称": "Edit group name", "分组名称": "Name", "请输入名称": "Please enter the name", "克隆大盘成功": "Cloned successfully", "新建大盘成功": "Created successfully", "大盘名称": "Name", "分类标签": "Tags", "更新时间": "Update Time", "发布人": "Publisher", "克隆": "<PERSON><PERSON>", "是否删除大盘": "Sure to delete the dashboard?", "删除大盘成功": "Deleted successfully", "导入": "Import", "未选择任何大盘": "No dashboard chosen", "导出": "Export", "监控大盘": "Dashboards", "新建大盘": "New Dashboard", "导入内置大盘": "Import built-in dashboard", "更多操作": "More", "克隆监控大盘": "Clone dashboard", "创建新监控大盘": "New Dashboard", "请输入大盘名称": "Enter dashboard name", "大盘": "Dashboard", "时间": "Time", "值": "Value", "告警详情": "Event Detail", "策略名称:": "Rule name", "通知结果": "Notice", "已触发": "Triggered", "已屏蔽": "Muted", "表达式": "Expression", "级别": "Level", "策略名称": "Rule name", "标签": "Tags", "通知": "Notification", "状态": "Status", "发生时间": "When", "屏蔽": "Mute", "是否忽略告警?": "Sure to ignore the event?", "忽略": "Ignore", "未恢复告警事件": "Triggered Events", "策略名称、标签、资源分组": "Rule name, tag, classpath", "告警级别": "Level", "告警状态": "Status", "是否批量忽略告警?": "Sure to ignore the events?", "已忽略告警": "Ignored successfully", "批量忽略": "<PERSON><PERSON> Ignore", "滴滴夜莺": "N9E", "请输入用户名": "Enter a username", "请输入密码": "Enter a password", "登录": "Sign in", "已选择": "Selected ", "项": "", "用户名、邮箱或电话": "User name, email or phone number", "用户创建成功": "User created successfully", "用户信息修改成功": "User Modified", "团队创建成功": "Team created successfully", "团队信息修改成功": "Team updated successfully", "密码重置成功": "Password reset successfully", "添加成功": "Added successfully", "取消": "Cancel", "确认": "Confirm", "确认并搜索": "Confirm and search", "用户删除成功": "User deleted successfully", "团队删除成功": "Team deleted successfully", "创建团队": "New Team", "成员删除成功": "Deleted member successfully", "确定": "OK", "确定要删除么？": "Sure to delete it?", "密码": "Password", "请输入密码!": "Please enter the password!", "确认密码": "Confirm password", "请确认密码!": "Please confirm the password!", "团队名称": "Team Name", "团队名称不能为空！": "Team name can not be blank!", "备注": "Remark", "管理员": "Administrator", "普通用户": "Ordinary users", "游客": "Visitors", "用户名不能为空！": "User name can not be blank!", "角色": "Role", "角色不能为空！": "Role is required", "启用": "Enable", "编辑": "Edit", "重置密码": "Reset Password", "是否删除该用户": "Sure to delete the user?", "是否删除该成员": "Sure to delete the member?", "禁用成功": "Disabled successfully", "启用成功": "Enabled successfully", "用户管理": "Users", "用户名、邮箱或手机": "User name, email or mobile phone", "创建": "Create", "创建并关闭": "Create and close", "创建用户": "Create User", "团队管理": "Team Management", "搜索团队名称": "Team name", "是否删除该团队": "Sure to delete the team?", "成员名、邮箱或手机": "Name, email or mobile", "添加成员": "Add Member", "选择监控指标添加图表": "Select the metric to add the chart", "即时看图": "Explorer", "监控指标": "Metric", "筛选标签": "Tags", "配置": "Configuration", "每张图最多展示": "Maximum", "条线": "lines per chart", "重置": "Reset", "分享图表": "Share", "资源标识": "Resource ID", "资源名称": "Name", "资源信息或标签": "Resource information or labels", "请选择标签": "Select a tag", "删除指标成功": "Deleted successfully", "创建指标成功": "Created successfully", "请勾选需要导出的指标": "Please check the metrics you need to export", "编辑成功": "Edit successfully", "指标名称": "Metric", "释义": "Description", "确定删除该指标?": "Sure to delete this metric?", "保存": "Save", "指标释义": "Summary", "指标名称或释义": "Search", "不能为空": "Cannot be blank", "已发送": "<PERSON><PERSON>", "近": "Near", "day-events": "Day Events", "天未恢复的": "Days Events", "进入事件管理": "Go to events page", "一分钟自动刷新": "One minute refresh", "指标TOP": "Metric TOP", "个": "", "资源总量": "Resources", "策略总量": "Rules", "大盘总量": "Dashboards", "用户总量": "Users", "团队总量": "Teams", "统计数据": "Statistics", "导入规则": "Import Rules", "导出规则": "Export rules", "请输入导入信息": "Please input import json", "是否确定删除资源分组?": "Sure to delete classpath?", "分组名称前缀匹配(聚合展示所有相关分组的内容)": "Prefix matching (aggregation displays all relevant grouping content)", "count(对符合规则的日志进行计数)": "count(count the matching lines)", "histogram(对符合规则的日志抓取出的数字计算统计值)": "histogram", "秒": "s", "不符合输入规范（格式为key=value）": "format should be 'key=value'", "资源": "Resources", "看图": "Explorer", "端口": "Port", "进程": "Process", "插件": "Plugin", "日志": "Log", "端口号": "Port", "进程标识": "Process identification", "日志路径": "Log path", "采集名称": "Collect rule", "采集频率": "Step", "修改人": "Modifier", "修改时间": "Last update", "是否删除该采集策略?": "Sure to delete this collection rule?", "是否删除该告警规则?": "Sure to delete this alert rule?", "删除成功": "Deleted successfully", "挂载资源": "Bind Resources", "请输入资源标识": "Input resource identifications, one resource per line", "请输入资源标识，每行一个": "Input resource identifications, one resource per line", "新建资源分组": "New", "编辑资源分组": "Edit classpath", "刷新": "Refresh", "资源分组路径": "Classpath", "资源分组路径必填": "Classpath cannot be blank", "请填写资源分组路径": "Input classpath", "资源分组备注": "Remark", "创建进程采集": "New collection", "进程采集详情": "Collection rule(Process)", "更新": "Update", "采集规则成功": " collection rule successfully", "进程监控指标": "Metric", "所属资源分组": "Classpath", "资源分组必填": "Classpath is required", "采集名称必填": "Rule name required", "请输入采集配置的名称": "Please enter rule name", "采集方式": "Method", "采集方式必填": "Collection mode is required", "命令行": "cmdline", "进程名": "name", "该项必填": "Required", "取 /proc/$pid/cmdline 中的部分子串，可以唯一标识进程即可": "Take some substrings from /proc/$pid/cmdline to uniquely identify the process", "取  /proc/$pid/status 中 name 的值": "Take the value of name in /proc/$pid/status", "采集频率必填": "Step required", "附加标签": "Append tags", "格式为key=value回车分隔": "The format is key=value, use Enter to separate", "创建端口采集": "New collection", "端口采集详情": "Collection rule(Port)", "端口监控指标": "Metric", "端口协议": "Port Protocol", "端口协议必填": "Port protocol required", "端口号必填": "Port required", "请输入端口号": "Enter port", "连接超时": "Connection timeout", "连接超时必填": "Connection timeout required", "创建插件采集": "New Collection", "插件采集详情": "Collection rule(Plugin)", "插件采集成功": " collection successfully ", "插件路径": "Plugin filepath", "插件路径必填": "Plugin filepath required", "参数": "Parameters", "请输入参数": "Enter parameters", "环境变量": "Env variables", "变量": "Variable", "标准输入(Stdin)": "Standard input (Stdin)", "创建日志采集": "New collection", "日志采集详情": "Collection rule(Log)", "指标名称必填": "Metric required", "计算方法": "Calculation method", "计算方法必填": "Calculation method required", "日志路径必填": "Log path required", "匹配正则": "Main regexp", "匹配正则必填": "Main regexp required", "请输入正则表达式匹配日志关键字，histogram会提取第一个括号内的值": "Enter regexp to match logs, histogram will use first substring value", "耗时计算：中的数值会用于计算曲线值流量计数：每匹配到该正则，曲线值+1": "Time-consuming calculation: the values in the calculation are used to calculate the number of curved-value flowmeters: for each match to the regular, the curves are + 1", "tagName 填写说明\\r\\n\n              1. 不允许包含host、trigger、include\n              2. 不允许包含如下4个特殊字符\n              tagValue 填写说明\n              1.必须包含括号。括号中的正则内容被用作tagValue的取值，必须可枚举。\n              2.不允许包含如下4个特殊字符=,:@": "TagName  Fill in the instructions \n              1.  No host, no, no, no, no. \n              2.  Not allowed to include the following 4 special characters \n              tagValue  Fill in instructions \n              1. Must contain parentheses. The regular content in parentheses is used as a value for tagValue, and must be enumerated. \n              2. The following 4 special characters are not allowed =,: @", "验证": "Verify", "请输入一行待监控的完整日志": "Enter a full line of logs to be monitored", "通过": "Success", "失败": "Failure", "屏蔽时间": "Mute time", "选择时间": "Choose times", "克隆到其他资源分组": "Cloning to other classpath", "选择资源分组": "Select classpaths", "克隆成功": "Cloned successfully", "分组不可为空": "Group can not be blank", "是否确认永久删除资源": "Sure to delete the resource ?", "无": "None", "资源详情": "Basic Information", "请选择所属资源分组": "Select the classpaths", "彻底删除资源": "Delete", "两种情况会有彻底删除资源的需求": "There are two cases to delete resources", "资源对应的设备已经下线了，永远都不会有新的监控数据上报了": "The resource has been offline", "资源的ident(唯一标识)发生变化，老的ident需要手工清理掉": "The ident of the resource changes", "未选择任何规则": "No rules chosen", "是否批量删除规则?": "Sure to delete the rules?", "组件采集信息": "Component Collection", "端口信息": "Port", "复制已选项": "<PERSON><PERSON>d successfully", "复制": "<PERSON>pied ", "条数据到剪贴板": " records", "请先选择资源标识": "Select the resource logo first", "是否移除该资源?": "Remove this resource from the classpath?", "移除": "Unbind", "您未选用任何资源": "No resources chosen", "资源管理": "Resources", "周期内所有点": "all", "在统计周期 $n 秒内（初次上报的指标，统计周期内只有一个值，如要限制点数，可选择happen函数）每个值都 $v": "All the values $v in $n seconds (To limit the number of points, use the happen function)", "发生次数": "happen", "统计周期 $n 秒内至少有 $m 次值 $v": "There are at least $m times in $n seconds with the value $v", "最大值": "max", "统计周期 $n 秒内的最大值 $v": "The maximum value $v in $n seconds", "最小值": "min", "统计周期 $n 秒内的最小值 $v": "The minimum value $v in $n seconds", "均值": "avg", "统计周期 $n 秒内的均值 $v": "The average value $v in $n seconds", "求和": "sum", "统计周期 $n 秒内的所有值求和 $v": "The sum value $v in $n seconds", "突增突降值": "diff", "最新值与其之前 $n 秒内的任意值之差 (区分正负) $v": "(the difference between the latest value and any previous value of $n seconds) divided by the corresponding historical value (positive and negative) $v %", "突增突降率": "pdiff", "(最新值与其之前 $n 秒的任意值之差)除以对应历史值 (区分正负) $v ％": "(Latest value before  $n  The difference between any value of the second (divided by the corresponding historical value)  (distinguishing between positive and negative)  $v  %", "同比变化率": "c_avg_rate_abs", "最近 $n 秒平均值相对 $m 天前同周期平均值变化率 $v ％": "The change rate of last $n seconds average relative to $m days average in the same period $v %", "同比变化率(区分正负)": "c_avg_rate", "同比变化值": "c_avg_abs", "最近 $n 秒平均值相对 $m 天前同周期平均值变化值 $v": "The change value of last $n seconds average relative to $m days average in the same period $v", "同比变化值(区分正负)": "c_avg", "3-sigma离群点检测": "stddev", "统计周期 $n 秒内波动值过大，超过了 $m 个标准差范围": "The statistical period fluctuates too much in $n seconds, exceeding $m standard deviation range", "新建告警屏蔽": "New mute rule", "告警屏蔽": "Mute Rules", "请检查": "Please check ", "中的空格": " space", "新建告警屏蔽成功": "Mute rule created successfully", "屏蔽指标": "Mute metric", "请输入指标名称": "Input metric", "请输入资源标识的正则表达式": "Input a regexp for the resource identification", "屏蔽标签": "Mute tags", "请输入屏蔽标签(请用回车分割)": "Input mute tags (use En<PERSON> to split)", "屏蔽原因": "<PERSON><PERSON>", "请填写屏蔽原因": "Enter mute cause", "屏蔽标识、资源标识、屏蔽标签、资源分组前缀不能同时为空": "The metric, ID, tags ,Prefix coult not be empty at the same time", "暂无": "None", "告警策略详情": "Alert Strategy Details", "解除成功": "Removed successfully", "开始:": "Start:", "结束:": "End:", "创建人": "Creator", "确定解除该告警屏蔽?": "Sure to remove the mute rule?", "解除": "Remove", "屏蔽策略": "Mute Rules", "指标名称、资源标识、标签、屏蔽原因": "Metric, resource id, tag, mute cause", "是否确定删除该策略分组?": "Sure to delete the alert group?", "名称": "Name", "告警接收者": "Recipients", "未选择任何采集策略": "No rules selected", "是否批量删除告警规则?": "Sure to delete the alert rules?", "请输入告警策略名称": "Search rule name", "新建告警策略": "New alert rule", "导入内置策略": "Import built-in rules", "策略": " rules", "请输入标签key": "Enter the tag key", "请输入标签value": "Enter the tag value", "预览": "Preview", "请输入指标": "Enter metric", "请输入百分比": "Enter percentages", "新建策略分组": "New rule group", "新建子节点": "New child node", "编辑策略组": "Edit rule Group", "策略组名称": "Name", "策略组名称必填": "Group name required", "请填写策略组名称": "Please enter group name", "管理团队": "Manage teams", "请选择团队": "Select teams", "告警策略": "Alert <PERSON>", "请输入分钟数": "Enter minutes", "秒（告警恢复后持续观察": "Seconds (continued observation after alarm recovery)", "秒，未再触发阈值才发送恢复通知）": "Seconds, no more trigger threshold to send recovery notice)", "保存成功": "Successfully saved", "创建成功": "Created successfully", "克隆失败": "Cloning failed", "保存失败": "Unable to save", "创建失败": "Failed to create", "周一": "Monday", "周二": "Tuesday", "周三": "Wednesday", "周四": "Thursday", "周五": "Friday", "周六": "Saturday", "周日": "Sunday", "基本配置": "Basic configuration", "策略名称不能为空": "Rule name required", "请输入策略名称": "Enter the rule name", "一级报警": "Level 1", "二级报警": "Level 2", "三级报警": "Level 3", "触发模式": "Trigger mode", "统计周期": "Step", "触发条件": "Condition", "资源过滤": "Resource filter", "标签过滤": "Tag filter", "执行频率": "Exec interval", "请输入Promeql": "Enter Promeql", "请输入附加标签，格式为key=value": "Enter an additional tag in a key=value format", "生效配置": "Enable configuration", "立即启用": "Enable", "是": "Yes", "否": "No", "生效时间": "Time", "通知配置": "Notification configuration", "静默恢复": "Silent recovery", "不发送恢复通知": "Do not send a recovery notice", "通知媒介": "Media", "报警接收团队": "Notify teams", "报警接收人": "Notify users", "报警回调": "Callback", "通知自己开发的系统（请确认是夜莺服务端可访问的地址）": "Notify your system (make sure n9e-server could access the address)", "其他配置": "Other configuration", "预案链接": "Runbook", "监控看图": "Metrics", "策略配置": "Monitors", "未恢复告警": "Unrecovered", "用户": "Users", "团队": "Teams", "团队列表": "Team list", "新建团队": "New", "退出": "Logout", "收藏的": "Favorite ", "分组": "", "所有": "All ", "一行一个": "One per line", "策略分组": "Rule Groups", "新建": "New ", "请输入日志路径，支持*通配符": "Please enter logs filepath, support wildcards", "1小时": "1 hour", "2小时": "2 hours", "6小时": "6 hours", "12小时": "12 hours", "1天": "1 day", "2天": "2 days", "7天": "1 week", "14天": "2 weeks", "30天": "30 days", "90天": "90 days", "永久": "forever", "清除": "clear", "预览图": "Preview Chart", "批量删除": "Delete", "修改告警接受者": "Update recipients", "修改通知媒介": "Update medias", "策略启用控制": "Update status", "是否启停": "Enable", "修改附加标签": "Update append tags", "修改附加标签成功": "Update append tags successfully", "append_tags": "Tags", "请先选择策略": "Select some rules", "不符合输入规范(格式为key=value)": " format should be 'key=value'", "编辑用户信息": "Edit user", "编辑团队信息": "Edit team", "基本信息": "Basic info", "预案手册": "Runbook", "预警值": "<PERSON><PERSON><PERSON><PERSON>", "维护备注": "Update remark", "维护标签": "Update tags", "修改备注": "Update remark", "修改标签": "Update tags", "暂无数据": "None", "告警历史": "All Events", "全部告警": "All", "全量告警历史": "History Alert <PERSON>", "恢复": "Recovery", "告警": "<PERSON><PERSON>", "资源分组前缀": "Classpath Prefix", "搜索资源分组前缀": "Search Classpath Prefix", "key, value必填": "key,value required", "key重复": "Duplicate key", "自定义开始时间": "Cumson start time", "自定义结束时间": "<PERSON><PERSON><PERSON> end time", "最近": "Past ", "minute": " minute", "minutes": " minutes", "hour": " hour", "hours": " hours", "周": " week", "天": " day", "月": " month", "季度": " quarter", "tpl.create": "Create", "tpl.tag.bind": "Bind tags", "tpl.tag.unbind": "Unbind tags", "tpl.tag.bind.title": "Batch bind tags", "tpl.tag.bind.success": "Successfully binded", "tpl.tag.bind.field": "tags", "tpl.tag.unbind.title": "Batch unbind tags", "tpl.tag.unbind.field": "tags", "tpl.tag.unbind.success": "Successfully unbinded", "tpl.node.modify": "Modify node", "tpl.node.modify.title": "Batch modify node", "tpl.batch.modify.group": "Batch modify group", "tpl.title": "Title", "tpl.title.tpl.help": "Describe the role of the template", "tpl.title.task.help": "Describe the role of the task", "tpl.tags": "Tags", "tpl.tags.help": "Used for classification", "tpl.creator": "Creator", "tpl.last_updator": "Last updator", "tpl.last_updated": "Last updated", "tpl.account.help": "Execute account, use root with caution", "tpl.batch.help": "Concurrency, default is 0, indicating full concurrent execution, 1 means sequential execution, 2 means that each time two execute", "tpl.tolerance.help": "Tolerate several machines failing, the default is 0, which means no tolerance, once failed, immediately suspend", "tpl.timeout.help": "Timeout for stand-alone script execution, in seconds", "tpl.pause.help": "Pause after completed, pause multiple hosts, write multiple hosts, separated by commas", "tpl.host.help": "List of hosts to be executed", "tpl.script.help": "Script content to be executed", "tpl.args.help": "Parameters attached to the script, separated by double commas, such as arg1,,arg2,,arg3", "tpl.modify": "Modify the template", "tpl.create.task": "Create a new task", "task.create": "Create task", "task.title": "Title", "task.done": "Done", "task.clone": "<PERSON><PERSON>", "task.meta": "Meta", "task.creator": "Creator", "task.created": "Created", "task.only.mine": "Just mine", "task.host": "Host", "task.status": "Status", "task.output": "Output", "task.refresh": "Refresh", "task.control.params": "Control params", "task.account": "Account", "task.batch": "<PERSON><PERSON>", "task.tolerance": "Tolerance", "task.timeout": "Timeout", "task.script": "<PERSON><PERSON><PERSON>", "task.script.args": "<PERSON><PERSON><PERSON> params", "task.pause": "Pause", "task.host.list": "Host list", "task.clone.new": "<PERSON>lone a new task", "task.temporary.create": "Create temporary task", "groups.instGroups": "Instant groups", "groups.crontabGroups": "Crontab groups", "perms.hosts": "Hosts", "perms.host": "Host", "perms.team": "Team", "perms.account": "Account", "last.7.days": "Last 7 days", "last.15.days": "Last 15 days", "last.30.days": "Last 30 days", "last.60.days": "Last 60 days", "last.90.days": "Last 90 days", "table.nodata": "No data", "table.create": "Create", "table.operations": "Operations", "table.batch.operations": "Batch Operations", "table.detail": "Detail", "table.modify": "Modify", "table.delete": "Delete", "table.delete.batch": "Batch delete", "table.clone": "<PERSON><PERSON>", "table.delete.sure": "Are you sure to delete it?", "table.delete.there.sure": "Are you sure to delete these?", "table.ident": "Ident", "table.name": "Name", "table.cate": "<PERSON><PERSON>", "table.creator": "Creator", "table.lastupdated": "Last updated", "table.note": "Note", "form.submit": "Submit", "form.save": "Save", "form.submit.and.save": "Save", "form.login": "<PERSON><PERSON>", "form.goback": "Go back", "msg.submit.success": "Successfully submited", "msg.modify.success": "Successfully modified", "msg.create.success": "Successfully created", "msg.add.success": "Successfully created", "msg.delete.success": "Successfully deleted", "msg.clone.success": "Successfully cloned", "msg.clone.error": "Cloning failed", "msg.sort.success": "Successfully sorted"}