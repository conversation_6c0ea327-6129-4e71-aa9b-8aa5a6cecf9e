import { DiagnosticResult } from '../types/diagnostic';

export function formatDiagnosticResult(result: DiagnosticResult): string {
  const {
    id,
    workflow_id,
    sequence_number,
    status,
    outputs,
    elapsed_time,
    total_tokens,
    total_steps,
    created_at,
    finished_at,
    exceptions_count
  } = result;

  // 提取文本内容
  const content = outputs.text || '';

  // 构建 Markdown 文档
  const markdown = `# 诊断结果报告

## 基本信息
- **诊断ID**: \`${id}\`
- **工作流ID**: \`${workflow_id}\`
- **序号**: ${sequence_number}
- **状态**: ${status}
- **执行时间**: ${elapsed_time.toFixed(2)}秒
- **Token消耗**: ${total_tokens}
- **步骤数**: ${total_steps}
- **异常数**: ${exceptions_count}
- **创建时间**: ${new Date(created_at * 1000).toLocaleString()}
- **完成时间**: ${new Date(finished_at * 1000).toLocaleString()}

## 诊断内容
${content}

---
> **报告生成时间**: ${new Date().toLocaleString()}
> 
> **报告状态**: ${status === 'succeeded' ? '✅ 成功' : '❌ 失败'}
> 
> **执行效率**: ${(elapsed_time / total_steps).toFixed(2)}秒/步
`;

  return markdown;
} 