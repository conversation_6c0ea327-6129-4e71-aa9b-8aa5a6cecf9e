# 智能诊断模块配置管理

## 概述

智能诊断功能允许通过Dify API调用AI工作流或智能体，对系统进行智能化诊断分析。本文档说明如何管理诊断模块配置和配置文件更新流程。

## 配置文件

系统完全基于文件系统配置：

- **配置文件位置**：`public/defaults/diagnosis-modules.json`
- **配置读取**：系统启动时从该文件读取配置
- **配置更新**：通过UI导出配置文件，然后手动替换服务器上的文件

## 文件系统配置格式

`public/defaults/diagnosis-modules.json`文件格式示例：

```json
[
  {
    "id": "default-module-1",
    "name": "系统性能诊断",
    "description": "诊断系统性能问题并提供优化建议",
    "difyConfig": {
      "apiKey": "your-api-key-here",
      "endpoint": "https://api.dify.ai/v1",
      "workflowId": "your-workflow-id-here"
    }
  },
  {
    "id": "default-module-2",
    "name": "告警智能分析",
    "description": "分析告警事件并提供处理建议",
    "difyConfig": {
      "apiKey": "your-api-key-here",
      "endpoint": "https://api.dify.ai/v1",
      "workflowId": "your-other-workflow-id"
    }
  }
]
```

## 配置管理流程

1. **读取配置**：系统启动时读取`diagnosis-modules.json`文件
2. **修改配置**：通过UI添加、编辑或删除诊断模块
3. **导出配置**：修改后点击"导出配置"按钮下载新的配置文件
4. **更新配置**：手动替换服务器上的配置文件，使更改永久生效
5. **刷新配置**：系统会定期（每30秒）自动刷新配置，或者用户可以点击"刷新"按钮手动刷新

## 多环境部署

为了支持多环境部署（开发、测试、生产等），您可以：

1. 在不同环境的部署过程中，使用环境变量或配置替换`diagnosis-modules.json`文件
2. 使用界面中的配置导入/导出功能手动管理配置

## 用户界面操作

智能诊断页面提供以下配置管理功能：

1. **添加模块**：点击"添加诊断模块"按钮，填写名称、描述和Dify API配置
2. **编辑模块**：点击模块列表中的"编辑"按钮修改现有模块
3. **删除模块**：点击模块列表中的"删除"按钮删除模块
4. **导出配置**：点击导出按钮（↓）将当前配置导出为JSON文件
5. **导入配置**：点击导入按钮（↑）从JSON文件导入配置
6. **刷新配置**：点击刷新按钮重新从文件系统加载配置
7. **运行诊断**：点击"运行诊断"链接执行特定模块的诊断

## 技术限制说明

由于浏览器安全模型限制，前端应用无法直接修改服务器上的文件。因此，配置更改流程如下：

1. 用户在UI中进行更改
2. 系统生成并提供下载配置文件
3. 用户手动将配置文件替换至服务器适当位置
4. 系统在下次刷新时加载新配置

## 开发指南

### 修改默认配置

要修改默认配置，编辑`public/defaults/diagnosis-modules.json`文件并重新部署应用。

### 添加环境特定配置

在CI/CD流程中，您可以添加一个步骤来根据目标环境替换配置文件：

```bash
# 示例：根据环境变量选择配置文件
if [ "$ENV" = "production" ]; then
  cp configs/production/diagnosis-modules.json public/defaults/diagnosis-modules.json
elif [ "$ENV" = "staging" ]; then
  cp configs/staging/diagnosis-modules.json public/defaults/diagnosis-modules.json
else
  cp configs/development/diagnosis-modules.json public/defaults/diagnosis-modules.json
fi
```

## 常见问题

**Q: 我修改了配置，为什么刷新页面后看不到更新？**

A: 系统只从服务器文件加载配置。您需要导出配置文件并替换服务器上的文件，然后刷新页面。

**Q: 如何加快配置更新流程？**

A: 可以考虑实现后端API来处理配置文件更新，这样前端可以直接调用API更新配置，无需手动替换文件。

**Q: 如何在不同环境使用不同的API密钥？**

A: 为每个环境创建单独的配置文件，在部署过程中替换`public/defaults/diagnosis-modules.json`。

**Q: 配置中的API密钥是否安全？**

A: 配置中的API密钥将暴露在前端，建议使用有限权限的密钥，或者考虑通过后端代理API调用。

**Q: 修改配置后需要重启应用吗？**

A: 不需要。系统会定期检查配置文件变更（每30秒），或者您可以点击刷新按钮立即加载最新配置。 