name: Build With Vite

on:
  push:
    branches: [master, v5.1]
  pull_request:
    branches: [master, v5.1]

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [12.x]

    steps:
      - uses: actions/checkout@v2

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}

      - name: Build
        run: |
          npm install
          npm run build
